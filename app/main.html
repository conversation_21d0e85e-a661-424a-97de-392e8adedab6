<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TexSlide - Designed for Scientific Presentations</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-blue: #1a56db;
            --light-blue: #e1effe;
            --dark-blue: #1e429f;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333;
            line-height: 1.6;
        }
        .hero-bg {
            background: linear-gradient(135deg, var(--light-blue) 0%, #ffffff 100%);
        }
        .btn-primary {
            background-color: var(--primary-blue);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: var(--dark-blue);
            transform: translateY(-2px);
        }
        .feature-card {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
            border-color: var(--primary-blue);
        }
        .feature-icon {
            color: var(--primary-blue);
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        .highlight-section {
            background-color: var(--light-blue);
        }
        .latex-formula {
            font-family: 'Times New Roman', Times, serif;
            font-style: italic;
        }
        .navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .section-title {
            color: var(--primary-blue);
            position: relative;
            display: inline-block;
        }
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50px;
            height: 3px;
            background-color: var(--primary-blue);
        }
    </style>
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="navbar fixed w-full z-10 py-4">
        <div class="container mx-auto flex justify-between items-center px-6">
            <div class="flex items-center">
                <span class="text-2xl font-bold text-gray-800">
                    <span style="color: var(--primary-blue);">Tex</span>Slide
                </span>
            </div>
            <div class="hidden md:flex space-x-8">
                <a href="#features" class="text-gray-600 hover:text-gray-900">Features</a>
                <a href="#benefits" class="text-gray-600 hover:text-gray-900">Benefits</a>
                <a href="#download" class="text-gray-600 hover:text-gray-900">Download</a>
            </div>
            <button class="btn-primary">Get Started</button>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-bg pt-32 pb-20">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 md:pr-10">
                    <h1 class="text-4xl md:text-5xl font-bold mb-6">
                        <span style="color: var(--primary-blue);">TexSlide:</span> Designed for Scientific Presentations
                    </h1>
                    <p class="text-lg mb-8">
                        TexSlide is presentation software deeply customized based on LibreOffice Impress, designed for researchers, academics, and engineers requiring precise presentation of complex mathematical formulas. It deeply integrates your local LaTeX environment, allowing you to easily leverage LaTeX's powerful formula typesetting and custom macro support within a familiar slide editing interface.
                    </p>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <button class="btn-primary">Download Now</button>
                        <button class="border border-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-50">
                            Learn More
                        </button>
                    </div>
                </div>
                <div class="md:w-1/2 mt-10 md:mt-0">
                    <div class="bg-white p-4 rounded-lg shadow-xl">
                        <div class="p-4 bg-gray-50 rounded border border-gray-200">
                            <span class="latex-formula text-xl">
                                f(x) = \int_{-\infty}^{\infty} \hat{f}(\xi) e^{2\pi i \xi x} d\xi
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Core Highlights -->
    <section class="py-16">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-semibold text-center mb-16 section-title">Core Highlights</h2>
            <div class="grid md:grid-cols-3 gap-8">
                <div class="feature-card text-center">
                    <i class="fas fa-desktop feature-icon"></i>
                    <h3 class="text-xl font-semibold mb-3">Familiar Creation</h3>
                    <p>Built on LibreOffice Impress for easy adoption, ensuring you can start creating professional presentations immediately with minimal learning curve.</p>
                </div>

                <div class="feature-card text-center">
                    <i class="fas fa-square-root-alt feature-icon"></i>
                    <h3 class="text-xl font-semibold mb-3">Full LaTeX Power</h3>
                    <p>Supports all formula syntax and custom macros, bringing the complete typesetting capabilities of LaTeX to your presentations.</p>
                </div>

                <div class="feature-card text-center">
                    <i class="fas fa-align-left feature-icon"></i>
                    <h3 class="text-xl font-semibold mb-3">Elevated Formula Display</h3>
                    <p>Featuring revolutionary inline math integration, allowing formulas to seamlessly blend with text for professional-looking slides.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="highlight-section py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-semibold text-center mb-16 section-title">How TexSlide Enhances Your Presentations</h2>
            
            <div class="grid md:grid-cols-2 gap-10">
                <!-- Feature 1 -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-start">
                        <div class="mr-4">
                            <i class="fas fa-code text-3xl" style="color: var(--primary-blue);"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Direct LaTeX/XeLaTeX Call</h3>
                            <p>Leverages your environment for accurate formula typesetting. TexSlide directly communicates with your installed LaTeX system, ensuring consistent results between your papers and presentations.</p>
                        </div>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-start">
                        <div class="mr-4">
                            <i class="fas fa-puzzle-piece text-3xl" style="color: var(--primary-blue);"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Full Macro & Package Support</h3>
                            <p>Seamlessly compatible with your defined macros and common packages. Use the same custom commands and environments from your research papers in your slides without modification.</p>
                        </div>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-start">
                        <div class="mr-4">
                            <i class="fas fa-align-center text-3xl" style="color: var(--primary-blue);"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Seamless Inline Math</h3>
                            <p>Integrates perfectly within text with precise alignment for unprecedented flow. The revolutionary inline formula rendering ensures your mathematical expressions blend naturally with surrounding text.</p>
                        </div>
                    </div>
                </div>

                <!-- Feature 4 -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-start">
                        <div class="mr-4">
                            <i class="fas fa-vector-square text-3xl" style="color: var(--primary-blue);"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">High-Quality SVG</h3>
                            <p>Formulas are inserted as vector graphics (SVG), remaining sharp at any scale. Present on high-resolution displays or zoom in on complex equations without any loss of quality.</p>
                        </div>
                    </div>
                </div>

                <!-- Feature 5 -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-start">
                        <div class="mr-4">
                            <i class="fas fa-edit text-3xl" style="color: var(--primary-blue);"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Editable Source</h3>
                            <p>Easily edit the original LaTeX code by clicking the inserted formula. Make quick adjustments to your equations directly within the presentation interface without switching to external editors.</p>
                        </div>
                    </div>
                </div>

                <!-- Feature 6 -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex items-start">
                        <div class="mr-4">
                            <i class="fas fa-tachometer-alt text-3xl" style="color: var(--primary-blue);"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Optimized Performance</h3>
                            <p>Enhances rendering speed with multi-threading and caching. Work with complex presentations containing numerous formulas without sacrificing performance or responsiveness.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section id="benefits" class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-semibold text-center mb-16 section-title">Benefits for Academic Professionals</h2>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="p-6 bg-white rounded-lg shadow-md border-t-4" style="border-top-color: var(--primary-blue);">
                    <h3 class="text-xl font-semibold mb-3">Consistency Across Documents</h3>
                    <p>Maintain consistent notation and styling between your research papers and presentations, presenting a unified professional image.</p>
                </div>
                
                <div class="p-6 bg-white rounded-lg shadow-md border-t-4" style="border-top-color: var(--primary-blue);">
                    <h3 class="text-xl font-semibold mb-3">Time-Saving Workflow</h3>
                    <p>Copy formulas directly from your papers to your presentations without reformatting, saving valuable preparation time before conferences.</p>
                </div>
                
                <div class="p-6 bg-white rounded-lg shadow-md border-t-4" style="border-top-color: var(--primary-blue);">
                    <h3 class="text-xl font-semibold mb-3">Professional Appearance</h3>
                    <p>Impress your audience with typographically perfect mathematical expressions that enhance the credibility of your research presentations.</p>
                </div>
            </div>
            
            <div class="mt-12 text-center">
                <p class="text-lg mb-8">Join thousands of researchers worldwide who have elevated their presentations with TexSlide</p>
                <button class="btn-primary">Download TexSlide Today</button>
            </div>
        </div>
    </section>

    <!-- Example Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-semibold text-center mb-12 section-title">See TexSlide in Action</h2>
            
            <div class="bg-white p-8 rounded-lg shadow-lg mx-auto max-w-4xl">
                <h3 class="text-2xl mb-4">Example: Maxwell's Equations</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="p-4 bg-gray-50 rounded border border-gray-200">
                        <p class="mb-2 font-semibold">Differential Form:</p>
                        <div class="latex-formula text-xl">
                            \nabla \cdot \mathbf{E} = \frac{\rho}{\varepsilon_0}
                        </div>
                        <div class="latex-formula text-xl mt-2">
                            \nabla \cdot \mathbf{B} = 0
                        </div>
                        <div class="latex-formula text-xl mt-2">
                            \nabla \times \mathbf{E} = -\frac{\partial \mathbf{B}}{\partial t}
                        </div>
                        <div class="latex-formula text-xl mt-2">
                            \nabla \times \mathbf{B} = \mu_0\mathbf{J} + \mu_0\varepsilon_0\frac{\partial \mathbf{E}}{\partial t}
                        </div>
                    </div>
                    
                    <div class="p-4 bg-gray-50 rounded border border-gray-200">
                        <p class="mb-2 font-semibold">Inline Examples:</p>
                        <p>The charge density <span class="latex-formula">\rho</span> is related to the electric field <span class="latex-formula">\mathbf{E}</span> through Gauss's law.</p>
                        <p class="mt-2">When <span class="latex-formula">\nabla \cdot \mathbf{B} = 0</span>, it implies there are no magnetic monopoles.</p>
                        <p class="mt-2">The relation <span class="latex-formula">\mathbf{J} = \sigma \mathbf{E}</span> describes Ohm's law in differential form.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-semibold text-center mb-16 section-title">What Researchers Say</h2>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="p-6 bg-white rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="mb-4">"TexSlide has revolutionized how I present my research. The ability to use my custom LaTeX macros directly in presentations saves me hours of formatting work."</p>
                    <div>
                        <p class="font-semibold">Dr. Emily Chen</p>
                        <p class="text-sm text-gray-600">Theoretical Physicist</p>
                    </div>
                </div>
                
                <div class="p-6 bg-white rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    <p class="mb-4">"The inline formula rendering in TexSlide is exceptional. For the first time, my presentation slides look as professional as my published papers."</p>
                    <div>
                        <p class="font-semibold">Prof. Michael Rodriguez</p>
                        <p class="text-sm text-gray-600">Applied Mathematics</p>
                    </div>
                </div>
                
                <div class="p-6 bg-white rounded-lg shadow-md">
                    <div class="flex items-center mb-4">
                        <div class="text-yellow-400">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                        </div>
                    </div>
                    <p class="mb-4">"As an engineering professor, I need to show complex equations in my lectures. TexSlide's familiar interface combined with LaTeX power is exactly what I needed."</p>
                    <div>
                        <p class="font-semibold">Dr. James Wilson</p>
                        <p class="text-sm text-gray-600">Electrical Engineering</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Download Section -->
    <section id="download" class="highlight-section py-20">
        <div class="container mx-auto px-6 text-center">
            <h2 class="text-3xl font-semibold mb-8">Ready to Transform Your Presentations?</h2>
            <p class="text-lg mb-10 max-w-2xl mx-auto">Join thousands of researchers, academics, and engineers who have enhanced their scientific presentations with TexSlide.</p>
            
            <div class="bg-white p-8 rounded-lg shadow-lg inline-block">
                <h3 class="text-xl font-semibold mb-4">Download TexSlide</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="#" class="btn-primary flex items-center justify-center">
                        <i class="fab fa-windows mr-2"></i> Windows
                    </a>
                    <a href="#" class="btn-primary flex items-center justify-center">
                        <i class="fab fa-apple mr-2"></i> macOS
                    </a>
                    <a href="#" class="btn-primary flex items-center justify-center">
                        <i class="fab fa-linux mr-2"></i> Linux
                    </a>
                </div>
                <p class="mt-4 text-sm text-gray-600">Version 1.2.3 | Released: October 2023</p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row justify-between">
                <div class="mb-6 md:mb-0">
                    <h2 class="text-2xl font-bold mb-4">
                        <span style="color: var(--light-blue);">Tex</span>Slide
                    </h2>
                    <p class="max-w-xs">Advanced presentation software for researchers and academics, combining the power of LaTeX with the ease of LibreOffice Impress.</p>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-blue-300">Documentation</a></li>
                        <li><a href="#" class="hover:text-blue-300">Tutorials</a></li>
                        <li><a href="#" class="hover:text-blue-300">Support</a></li>
                        <li><a href="#" class="hover:text-blue-300">Community Forum</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Connect</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-2xl hover:text-blue-300"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-2xl hover:text-blue-300"><i class="fab fa-github"></i></a>
                        <a href="#" class="text-2xl hover:text-blue-300"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-700 text-center">
                <p>&copy; 2023 TexSlide. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
