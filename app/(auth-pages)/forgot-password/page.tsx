import { forgotPasswordAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import Image from "next/image";
import { SmtpMessage } from "../smtp-message";

export default async function ForgotPassword(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-xl">
        <div className="text-center">
          <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
            <Image
              src="/texslide.png"
              alt="TexSlide Logo"
              width={32}
              height={32}
              className="inline-block"
            />
            <span style={{ color: 'var(--primary-blue)' }}>Tex</span>Slide
          </h1>
          <h2 className="mt-6 text-2xl font-medium">Reset Password</h2>
          <p className="mt-2 text-sm text-gray-600">
            Enter your email address and we'll send you a link to reset your password
          </p>
        </div>

        <form action={forgotPasswordAction} className="mt-8 space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email</Label>
              <Input
                name="email"
                type="email"
                placeholder="<EMAIL>"
                required
                className="mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-blue focus:border-primary-blue border-gray-300"
              />
            </div>
          </div>

          <div>
            <SubmitButton
              pendingText="Sending Reset Link..."
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-blue hover:bg-primary-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue"
            >
              Send Reset Link
            </SubmitButton>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Remember your password?{" "}
              <Link className="font-medium text-primary-blue hover:text-primary-blue-dark" href="/sign-in">
                Sign in
              </Link>
            </p>
          </div>

          {searchParams && <FormMessage message={searchParams} />}
        </form>
      </div>
      <div className="mt-4">
        <SmtpMessage />
      </div>
    </div>
  );
}
