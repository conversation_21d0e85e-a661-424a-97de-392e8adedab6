'use client';

import { signInWithEmail, signInWithGithub } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import {useRouter } from 'next/navigation';
import { useState, useEffect } from "react";
import { detectUserRegion } from "@/utils/region-utils";

// 邮箱验证正则表达式
const EMAIL_REGEX = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;

export default function Login(props: { searchParams: Promise<Message> }) {
  const router = useRouter();
  const [message, setMessage] = useState<Message | null>(null);
  const [formErrors, setFormErrors] = useState({
    email: '',
  });
  const [formData, setFormData] = useState({
    email: '',
  });
  const [userRegion, setUserRegion] = useState<string | null>(null);
  const [isEmailSent, setIsEmailSent] = useState(false);

  // 从 URL 参数中获取错误信息和邮箱，并检测用户区域
  useEffect(() => {
    // Get URL parameters
    const searchParams = new URLSearchParams(window.location.search);
    const error = searchParams.get('error');
    const email = searchParams.get('email');

    if (error) {
      setMessage({ error: decodeURIComponent(error) });
    }
    if (email) {
      setFormData(prev => ({ ...prev, email: decodeURIComponent(email) }));
    }

    // Detect user region
    async function getUserRegion() {
      try {
        const region = await detectUserRegion();
        setUserRegion(region);
        console.log("Detected user region during sign-in:", region);
      } catch (error) {
        console.error("Error detecting user region during sign-in:", error);
      }
    }

    getUserRegion();
  }, []);


  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setFormErrors(prev => ({ ...prev, [name]: '' }));
  };

  // 验证表单
  const validateForm = (formData: FormData): boolean => {
    const email = formData.get('email')?.toString() || '';
    let isValid = true;
    const errors = {
      email: '',
    };

    // 验证邮箱
    if (!email) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!EMAIL_REGEX.test(email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  async function handleEmailSignIn(formData: FormData) {
    // 重置错误信息
    setFormErrors({ email: '' });
    setMessage(null);

    // 验证表单
    if (!validateForm(formData)) {
      return;
    }

    // Add user region to form data if available
    if (userRegion) {
      formData.append('userRegion', userRegion);
    }

    const result = await signInWithEmail(formData);
    if (result.success) {
      setIsEmailSent(true);
      setMessage({
        success: "Magic link sent! Please check your email and click the link to sign in."
      });
    } else {
      setMessage({"error" : result.error || "An error occurred"});
    }
  }

  async function handleGithubSignIn() {
    const { error } = await signInWithGithub();
    if (error) {
      setMessage({ error });
    }
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-lg shadow-xl">
        <div className="text-center">
          <h1 className="text-3xl font-bold">
            <span style={{ color: 'var(--primary-blue)' }}>Tex</span>Slide
          </h1>
          <h2 className="mt-6 text-2xl font-medium">Sign in to your account</h2>
        </div>

        {/* GitHub Sign In Button - Moved outside the form */}
        <div className="mb-6">
          <button
            type="button"
            onClick={handleGithubSignIn}
            className="w-full flex items-center justify-center gap-2 py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue"
          >
            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
            </svg>
            Sign in with GitHub
          </button>
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or sign in with email</span>
          </div>
        </div>

        {isEmailSent ? (
          <div className="mt-8 space-y-6 text-center">
            <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>
              <h3 className="text-lg font-medium text-green-800 mb-2">Magic Link Sent!</h3>
              <p className="text-sm text-green-700 mb-4">
                We've sent a magic link to <strong>{formData.email}</strong>
              </p>
              <p className="text-sm text-green-600">
                Please check your email and click the link to sign in. The link will expire in 1 hour.
              </p>
            </div>

            <button
              type="button"
              onClick={() => {
                setIsEmailSent(false);
                setMessage(null);
                setFormData({ email: '' });
              }}
              className="text-sm text-primary-blue hover:text-primary-blue-dark underline"
            >
              Send to a different email address
            </button>
          </div>
        ) : (
          <form action={handleEmailSignIn} className="mt-8 space-y-6">
            <div className="space-y-4">
              <div>
                <Input
                  name="email"
                  placeholder="Email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`mt-1 block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary-blue focus:border-primary-blue ${
                    formErrors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {formErrors.email && (
                  <p className="mt-1 text-sm text-red-500">{formErrors.email}</p>
                )}
              </div>
            </div>

            <div>
              <SubmitButton
                pendingText="Sending sign-in email..."
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-blue hover:bg-primary-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue"
              >
                 Sign in with Email
              </SubmitButton>
            </div>

            {message && <FormMessage message={message} />}
          </form>
        )}
      </div>
    </div>
  );
}
