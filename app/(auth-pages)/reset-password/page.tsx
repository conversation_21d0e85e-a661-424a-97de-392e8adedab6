"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Eye, EyeOff, KeyRound, RefreshCw } from "lucide-react";

// Password rules
const PASSWORD_RULES = [
  { text: "8-64 characters", regex: /^.{8,64}$/ },
  { text: "At least one uppercase letter", regex: /[A-Z]/ },
  { text: "At least one lowercase letter", regex: /[a-z]/ },
  { text: "At least one number", regex: /[0-9]/ },
  { text: "At least one special character", regex: /[!@#$%^&*(),.?":{}|<>]/ }
];

// Password strength calculation function
function calculatePasswordStrength(password: string): { score: number; label: string; color: string } {
  if (!password) return { score: 0, label: "Not entered", color: "bg-gray-200" };

  let score = 0;
  // Length check
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  // Character type check
  if (/[A-Z]/.test(password)) score += 1;
  if (/[a-z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[^A-Za-z0-9]/.test(password)) score += 1;

  // Return strength level based on score
  if (score <= 2) return { score, label: "Weak", color: "bg-red-500" };
  if (score <= 4) return { score, label: "Medium", color: "bg-yellow-500" };
  if (score <= 5) return { score, label: "Strong", color: "bg-green-500" };
  return { score, label: "Very Strong", color: "bg-emerald-500" };
}

// Generate random strong password
function generateStrongPassword() {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*(),.?":{}|<>';

  const allChars = uppercase + lowercase + numbers + symbols;
  let password = '';

  // Ensure each character type appears at least once
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];

  // Add additional random characters to make total length 12-16
  const additionalLength = Math.floor(Math.random() * 5) + 8; // 8-12 additional characters
  for (let i = 0; i < additionalLength; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Shuffle password characters
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

export default function ResetPassword() {
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [hasToken, setHasToken] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: "Not entered", color: "bg-gray-200" });
  const [formErrors, setFormErrors] = useState({ password: '' });
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    // 只有在没有 token 时才重置
    if (!hasToken) {
      setPassword("");
      setPasswordStrength({ score: 0, label: "Not entered", color: "bg-gray-200" });
    }
  }, [hasToken]); 

  // Get the hash fragment from the URL
  useEffect(() => {
    // Check if we have the necessary parameters for password reset
    console.log('Reset password page loaded');

    const searchParams = new URLSearchParams(window.location.search);
    const code = searchParams.get('token');

    async function verifyToken(code: string) {
        const { data: { user } } = await supabase.auth.getUser();

        console.log('User data:', user?.id);
        console.log('Token:', code);

        if (user?.id != code) {
          throw new Error("用户未登录");
        }
      }

    if (code) {
      console.log('Hash fragment found, password reset should be possible');
      verifyToken(code);
      setHasToken(true);
    } else {
      console.error('No hash fragment found in URL');
      setError("Invalid or missing reset token. Please request a new password reset link.");
      setHasToken(false);
    }
  }, []);

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('Password changed to:', e.target.value); // 添加这行
    const newPassword = e.target.value;
    setPassword(newPassword);
    setPasswordStrength(calculatePasswordStrength(newPassword));
    setFormErrors(prev => ({ ...prev, password: '' }));
  };

  const handleGeneratePassword = () => {
    const newPassword = generateStrongPassword();
    setPassword(newPassword);
    setPasswordStrength(calculatePasswordStrength(newPassword));
    setFormErrors(prev => ({ ...prev, password: '' }));
  };

  const validatePassword = (password: string): boolean => {
    if (!password) {
      setFormErrors(prev => ({ ...prev, password: 'Password cannot be empty' }));
      return false;
    }

    const failedRules = PASSWORD_RULES.filter(rule => !rule.regex.test(password));
    if (failedRules.length > 0) {
      setFormErrors(prev => ({ ...prev, password: 'Password does not meet the requirements' }));
      return false;
    }

    return true;
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!hasToken) {
      console.error('Attempted to reset password without a valid token');
      setError("Invalid or missing reset token. Please request a new password reset link.");
      return;
    }

    // Validate password
    if (!validatePassword(password)) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Attempting to update password');

      // The hash fragment contains the access token and type
      const { error, data } = await supabase.auth.updateUser({
        password: password
      });

      console.log('Password update response:', data ? 'Success' : 'No data returned');

      if (error) {
        console.error('Supabase returned an error during password update:', error);
        throw error;
      }

      console.log('Password updated successfully');
      setMessage("Your password has been updated successfully!");
      toast.success("Password updated successfully");

      // Redirect to sign-in page after a short delay
      console.log('Will redirect to sign-in page in 2 seconds');
      setTimeout(() => {
        console.log('Redirecting to sign-in page now');
        router.push("/sign-in");
      }, 2000);

    } catch (error: any) {
      console.error("Error resetting password:", error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
      setError(error.message || "Failed to reset password. Please try again.");
      toast.error("Failed to reset password");
    } finally {
      setLoading(false);
      console.log('Password reset process completed');
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
      <div className="w-full max-w-md bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-center text-gray-800 mb-8">
          Choose a new password
        </h1>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {message && (
          <div className="mb-4 p-3 bg-green-100 border border-green-200 text-green-700 rounded-md">
            {message}
          </div>
        )}

        <form onSubmit={handlePasswordReset} className="space-y-6">
          <div className="space-y-5">
            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleGeneratePassword}
                  className="flex items-center gap-1.5 text-xs text-primary-blue hover:text-primary-blue-dark transition-colors"
                  disabled={loading || !hasToken}
                >
                  <KeyRound className="h-3.5 w-3.5" />
                  <RefreshCw className="h-3.5 w-3.5" />
                  Generate Strong Password
                </Button>
              </div>
              <div className="relative mt-1">
                <Input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={password || ''} // 确保即使undefined/null也会转为空字符串
                  onChange={handlePasswordChange}  // 通过handlePasswordChange更新状态
                  placeholder="Your password"
                  minLength={8}
                  maxLength={64}
                  autoComplete="new-password" // 添加这行
                  className={`mt-1 block w-full px-4 py-2.5 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-all duration-200 ${
                    formErrors.password ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={loading || !hasToken}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 transition-colors"
                  onClick={() => {
                    console.log('Before toggle - password:', password); // 添加这行
                    setShowPassword(!showPassword);
                    console.log('After toggle - password:', password); // 添加这行
                  }}
                  disabled={loading || !hasToken}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </button>
              </div>
              {formErrors.password && (
                <p className="mt-1.5 text-sm text-red-500">{formErrors.password}</p>
              )}

              {/* Password Strength Meter */}
              <div className="mt-2">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-600">Password Strength:</span>
                  <span className={`text-xs font-medium ${passwordStrength.color.replace('bg-', 'text-')}`}>
                    {passwordStrength.label}
                  </span>
                </div>
                <div className="h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className={`h-full transition-all duration-300 ${passwordStrength.color}`}
                    style={{ width: `${(passwordStrength.score / 6) * 100}%` }}
                  />
                </div>
              </div>

              {/* Password Requirements */}
              <div className="mt-3 p-4 bg-gray-50 rounded-xl space-y-2">
                <p className="text-xs font-medium text-gray-700">Password Requirements:</p>
                {PASSWORD_RULES.map((rule, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
                      rule.regex.test(password) ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                    <span className={`text-xs transition-colors duration-200 ${
                      rule.regex.test(password) ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {rule.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <Button
            type="submit"
            className="w-full py-2.5 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-primary-blue hover:bg-primary-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue transition-all duration-200"
            disabled={loading || !hasToken}
          >
            {loading ? "Updating..." : "Update Password"}
          </Button>
        </form>
      </div>
    </div>
  );
}
