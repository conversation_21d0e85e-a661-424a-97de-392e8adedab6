'use client';

import { signUpAction, signInWithGithub } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useState, useTransition } from "react";
import { Eye, EyeOff } from "lucide-react";

// 邮箱验证正则表达式
const EMAIL_REGEX = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;

// 密码规则
const PASSWORD_RULES = [
  { text: "8-64 characters", regex: /^.{8,64}$/ },
  { text: "At least one uppercase letter", regex: /[A-Z]/ },
  { text: "At least one lowercase letter", regex: /[a-z]/ },
  { text: "At least one number", regex: /[0-9]/ },
  { text: "At least one special character", regex: /[!@#$%^&*(),.?":{}|<>]/ }
];

export default function Signup(props: {
  searchParams: Promise<Message>;
}) {
  // const router = useRouter();
  const [message, setMessage] = useState<Message | null>(null);
  const [isPending, startTransition] = useTransition();
  const [formErrors, setFormErrors] = useState({
    email: '',
    password: ''
  });
  const [isSuccess, setIsSuccess] = useState(false);
  const [email, setEmail] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [password, setPassword] = useState('');

  // 验证表单
  const validateForm = (formData: FormData): boolean => {
    const email = formData.get('email')?.toString() || '';
    const password = formData.get('password')?.toString() || '';
    let isValid = true;
    const errors = {
      email: '',
      password: ''
    };
    
    // 验证邮箱
    if (!email) {
      errors.email = 'Email is required';
      isValid = false;
    } else if (!EMAIL_REGEX.test(email)) {
      errors.email = 'Please enter a valid email address';
      isValid = false;
    }

    // 验证密码
    if (!password) {
      errors.password = 'Password cannot be empty';
      isValid = false;
    } else {
      const failedRules = PASSWORD_RULES.filter(rule => !rule.regex.test(password));
      if (failedRules.length > 0) {
        errors.password = 'Password does not meet the requirements';
        isValid = false;
      }
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    setFormErrors(prev => ({ ...prev, email: '' }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    setFormErrors(prev => ({ ...prev, password: '' }));
  };

  async function handleSubmit(formData: FormData) {
    // 重置错误信息
    // setFormErrors({ email: '', password: '' });

    setFormErrors(prev => ({
      ...prev,
      email: '',
      password: ''
    }));

    setEmail(formData.get('email')?.toString() || '');
    
    // 验证表单
    if (!validateForm(formData)) {
      console.log("Form is not valid");
      return;
    }

    startTransition(async () => {
      try {
        const result = await signUpAction(formData);
        if (result.success) {
          setIsSuccess(true);
        }
        setMessage(result.message);
      } catch (error) {
        setMessage({ error: 'An error occurred during sign up' });
      }
    });
  }

  async function handleGithubSignUp() {
    const { data, error } = await signInWithGithub(true);
    if (error) {
      setMessage({ error });
    }
  }

  if (isSuccess) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
        <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-2xl shadow-2xl">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold tracking-tight">
              <span className="text-primary-blue">Tex</span>Slide
            </h1>
            <h2 className="text-2xl font-semibold text-gray-900">Check Your Email</h2>
          </div>
          
          <div className="text-center space-y-4">
            <div className="p-4 bg-blue-50 rounded-xl">
              <p className="text-gray-700 leading-relaxed">
                You have successfully registered. Please check your email to confirm your account before logging into the Supabase dashboard. The confirmation link will expire in 10 minutes.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <div className="w-full max-w-md p-8 space-y-8 bg-white rounded-2xl shadow-2xl">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">
            <span className="text-primary-blue">Tex</span>Slide
          </h1>
          <h2 className="text-2xl font-semibold text-gray-900">Create Your Account</h2>
        </div>
        
        {/* GitHub Sign In Button */}
        {/* <div className="mb-6">
          <button
            type="button"
            onClick={handleGithubSignUp}
            className="w-full flex items-center justify-center gap-3 py-3 px-4 border border-gray-300 rounded-xl shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue transition-all duration-200"
          >
            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
              <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
            </svg>
            Continue with GitHub
          </button>
        </div>
        
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-4 bg-white text-gray-500">Or sign up with email</span>
          </div>
        </div> */}
        
        <form action={handleSubmit} className="mt-8 space-y-6">
          <div className="space-y-5">
            <div>
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email</Label>
              <Input
                name="email"
                type="email"
                value={email}
                placeholder="<EMAIL>"
                className={`mt-1 block w-full px-4 py-2.5 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-all duration-200 ${
                  formErrors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                onChange={handleEmailChange}
              />
              {formErrors.email && (
                <p className="mt-1.5 text-sm text-red-500">{formErrors.email}</p>
              )}
            </div>
            <div>
              <div className="flex items-center justify-between">
                <Label htmlFor="password" className="text-sm font-medium text-gray-700">Password</Label>
              </div>
              <div className="relative mt-1">
                <Input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={password}
                  placeholder="Your password"
                  maxLength={64}
                  className={`block w-full px-4 py-2.5 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-primary-blue transition-all duration-200 ${
                    formErrors.password ? 'border-red-500' : 'border-gray-300'
                  }`}
                  onChange={handlePasswordChange}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              {formErrors.password && (
                <p className="mt-1.5 text-sm text-red-500">{formErrors.password}</p>
              )}
              
              {/* Password Requirements */}
              <div className="mt-3 p-4 bg-gray-50 rounded-xl space-y-2">
                <p className="text-xs font-medium text-gray-700">Password Requirements:</p>
                {PASSWORD_RULES.map((rule, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
                      rule.regex.test(password) ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                    <span className={`text-xs transition-colors duration-200 ${
                      rule.regex.test(password) ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {rule.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div>
            <SubmitButton
              pendingText="Signing up..."
              className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-xl shadow-sm text-sm font-medium text-white bg-primary-blue hover:bg-primary-blue-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue transition-all duration-200"
              disabled={isPending}
            >
              Sign Up
            </SubmitButton>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <Link className="font-medium text-primary-blue hover:text-primary-blue-dark transition-colors" href="/sign-in">
                Sign in now
              </Link>
            </p>
          </div>

          {message && <FormMessage message={message} />}
        </form>
      </div>
    </div>
  );
}
