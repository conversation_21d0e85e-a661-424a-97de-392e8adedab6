import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  // The `/auth/callback` route is required for the server-side auth flow implemented
  // by the SSR package. It exchanges an auth code for the user's session.
  // https://supabase.com/docs/guides/auth/server-side/nextjs
  const requestUrl = new URL(request.url);
  const error = requestUrl.searchParams.get("error");
  const code = requestUrl.searchParams.get("code");
  const origin = requestUrl.origin;
  const signup = requestUrl.searchParams.get("signup") === "true";
  const github = requestUrl.searchParams.get("github") === "true";
  let userId : string = "";

  if (error) {
    const error_description = requestUrl.searchParams.get("error_description")
    return NextResponse.redirect(`${origin}/error?message=${encodeURIComponent(error_description || 'Authentication error')}`);
  }

  if (code) {
    const supabase = await createClient();

    try {
      // 交换授权码获取会话
      const { data: { session }, error: sessionError } = await supabase.auth.exchangeCodeForSession(code);

      if (sessionError) {
        console.error('Session exchange error:', sessionError);
        throw sessionError;
      }

      // 获取用户信息
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('User fetch error:', userError);
        throw userError || new Error('No user found');
      }

      userId = user.id;

      console.log('User data:', {
        id: user?.id,
        email: user?.email,
        metadata: user?.user_metadata
      });

      // 检查用户是否已存在
      const { data: existingUser, error: checkError } = await supabase
        .from('users')
        .select('id, full_name, email')  // 添加 full_name 和 email 到查询
        .eq('id', user.id)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 表示没有找到记录
        console.error('Error checking user:', checkError);
        throw checkError;
      }

      let userFullName = '';
      let userEmail = user.email || '';

      // 如果用户不存在，创建新用户
      if (!existingUser) {
        if (signup || github) {
            // 尝试从用户元数据中获取全名
            userFullName = user.user_metadata?.full_name || user.user_metadata?.name || '';

            // 如果全名为空，则使用邮箱中 @ 符号前面的部分作为用户名
            if (!userFullName || userFullName.trim() === '') {
              const atIndex = userEmail.indexOf('@');
              if (atIndex > 0) {
                userFullName = userEmail.substring(0, atIndex);
                console.log("Using email username as default full name:", userFullName);
              }
            }

            const { error: insertError } = await supabase
              .from('users')
              .insert({
                id: user.id,
                email: userEmail,
                full_name: userFullName,
                is_active: true  // 默认值为 true，但显式设置更清晰
              });

            if (insertError) {
              console.error('Error inserting user:', insertError);
              throw insertError;
            }
            console.log('New user created successfully');
        } else {
          throw new Error('No user found');
        }
      } else {
        // 使用数据库中的现有数据
        userFullName = existingUser.full_name;
        userEmail = existingUser.email;

        const { error: updateError } = await supabase
          .from('users')
          .update({
            last_login_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        if (updateError) {
          console.error('Error updating user:', updateError);
          throw updateError;
        }
        console.log('User login time updated');
      }

      // 更新 session 中的用户数据
      const { error: updateSessionError } = await supabase.auth.updateUser({
        data: {
          full_name: userFullName,
          email: userEmail
        }
      });

      if (updateSessionError) {
        console.error('Error updating session:', updateSessionError);
        throw updateSessionError;
      }

      console.log('Session updated with user data:', {
        full_name: userFullName,
        email: userEmail
      });

    } catch (error: any) {
      console.error('Auth callback error:', error);
      const errorMessage = error.message || 'An error occurred during authentication';
      return NextResponse.redirect(`${origin}/error?message=${encodeURIComponent(errorMessage)}`);
    }
  }

  const defaultRedirect = `${origin}/dashboard`;
  console.log('Redirecting to default:', defaultRedirect);
  return NextResponse.redirect(defaultRedirect);
}
