import { createClient } from "@/utils/supabase/client";
import { TexSlideLicense } from "@/components/texslide-license";
import { fetchLicense } from "@/components/license-generator";
import { createHash, createCipheriv, createPrivateKey, randomBytes, sign } from 'crypto';

type ResponseData = {
    success: boolean;
    message: string;
}

const LICENSE_PRIVATE_KEY_HEX : string ='32c1800ba211994c43235f8b9785dac1b95e4d4d6cab90273329d1425c83ffd7';

function encodeLicense(key: Buffer, plaintext: Buffer) {
    const iv = randomBytes(12);

    const cipher = createCipheriv('aes-256-gcm', key, iv, {
      authTagLength: 16
    });

    // 执行加密
    const ciphertext = Buffer.concat([
      cipher.update(plaintext),
      cipher.final()
    ]);

    // 获取认证标签
    const tag = cipher.getAuthTag();

    return { ciphertext, iv, tag };
};

export async function POST(request: Request) {
    const body = await request.json();
    const { userName , licenseKey, fingerprint, OSName } = body;
    const supabase = await createClient();

    console.log("userName: ", userName);
    console.log("licenseKey: ", licenseKey);
    console.log("fingerprint: ", fingerprint);
    console.log("OS: ", OSName);

    // 获取用户信息
    const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('email', userName)
    .single();

    if (userError) {
        return new Response(JSON.stringify({success: false, message: 'no valid username.'}), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }

    // 获取并更新现有 License
    const { data: existingLicense, error: LicenseError } = await supabase
    .from('licenses')
    .select('*')
    .eq('user_id', userData.id)
    .eq('license_key', licenseKey)
    .single();

    if (LicenseError) {
        return new Response(JSON.stringify({success: false, message: 'no valid license.'}), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }

    // 获取当前License的设备列表
    const { data, error: DeviceError } = await supabase
    .from('devices')
    .select('*')
    .eq('license_id', existingLicense.id);
    if (DeviceError) {
        return new Response(JSON.stringify({success: false, message: 'error on fetching devices.'}), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
        });
    }
    const existingDevices = data || [];

    if (existingDevices.length >= existingLicense.max_devices) {
        return new Response(JSON.stringify({success: false, message: 'device limit reached.'}), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
        });
    }

    // 当前设备是否已激活
    const currentDevice = existingDevices.find(device => device.device_id === fingerprint);
    if (!currentDevice) {
       // 插入device
        try {
            console.log("inserting device: ", fingerprint, " ",  existingLicense.id);
            const { data: newDevice, error: insertError }= await supabase
            .from('devices')
            .insert({
                license_id: existingLicense.id,
                device_id: fingerprint,
                device_name: OSName,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            })
            .select()
            .single();
            console.log("inserting device success");

        } catch (error) {
            console.error('Error inserting device:', error);
            return new Response(JSON.stringify({success: false, message: 'error on inserting device.'}), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }
    }

    // 更新License状态
    let license : TexSlideLicense = await fetchLicense(userData.id, existingLicense.id);
    license.data.attributes.fingerprint = fingerprint;
    console.log("license: ", license);

    const digest = createHash('sha256');
    digest.update( Buffer.from(license.data.attributes.key) );
    const hexKey: Buffer = digest.digest();
    let plaintext: string = JSON.stringify(license);
    const { ciphertext, iv, tag } = encodeLicense(hexKey, Buffer.from(plaintext));

    const enc = [ciphertext, iv, tag].map(p => Buffer.from(p).toString('base64')).join('.');
    const licenseBuffer = Buffer.from(`license/${enc}`);

    // 从十六进制密钥中获取私钥
    const privateKey = createPrivateKey({
      key: {
        kty: 'OKP',
        crv: 'Ed25519',
        d: Buffer.from(LICENSE_PRIVATE_KEY_HEX, 'hex').toString('base64'),
        x: Buffer.alloc(32).toString('base64')
      },
      format: 'jwk'
    });

    // 使用私钥签名
    const sig = sign(null, licenseBuffer, privateKey).toString('base64');
    const alg : string = 'aes-256-gcm+ed25519';

    const message : string = '-----BEGIN LICENSE FILE-----\n'
     + Buffer.from(JSON.stringify({'enc': enc, 'sig': sig, 'alg': alg})).toString('base64')
     + '\n-----END LICENSE FILE-----\n';

    console.log(message);

    return new Response(JSON.stringify({success: true, message: message}), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
    });
}