import { NextResponse } from "next/server";
import { generateLicenseKey } from "@/components/license-generator";
import { createClient } from "@/utils/supabase/server";

export async function POST(request: Request) {
  try {
    // Parse the request body to get the selected user ID and license duration
    const { userId, months = 12 } = await request.json();

    // Generate a license key
    const licenseKey = generateLicenseKey();

    // Create a Supabase client
    const supabase = await createClient();

    // Get the current user's ID for audit purposes (admin who is creating the license)
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized. You must be logged in to generate a super license." },
        { status: 401 }
      );
    }

    // If no user is selected, use an empty string for user_id
    // Otherwise, use the selected user's ID
    const targetUserId = userId || user.id;

    // Set dates for the license
    const now = new Date();
    // Set expiry date based on the specified number of months (using proper month calculation)
    const expiryDate = new Date(now);
    expiryDate.setMonth(expiryDate.getMonth() + months);

    // Insert the license into the database
    const { data, error } = await supabase
      .from('licenses')
      .insert({
        user_id: targetUserId,
        license_key: licenseKey,
        status: 'ACTIVE',
        start_date: now.toISOString(),
        end_date: expiryDate.toISOString(),
        max_devices: 10, // Super licenses can be used on more devices
        is_super: true, // Set is_super to true as requested
        features: JSON.stringify({
          isPro: true,
          isEnterprise: true,
          allowedExports: ["pdf", "pptx", "html", "png", "svg"]
        })
      })
      .select()
      .single();

    if (error) {
      console.error("Error inserting super license:", error);
      return NextResponse.json(
        { error: "Failed to save super license key" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      licenseKey,
      id: data.id,
      created: data.created_at,
      expiry: data.end_date
    });
  } catch (error) {
    console.error("Error generating super license key:", error);
    return NextResponse.json(
      { error: "Failed to generate super license key" },
      { status: 500 }
    );
  }
}

// Keep the GET method for backward compatibility
export async function GET() {
  try {
    // Generate a license key
    const licenseKey = generateLicenseKey();

    // Create a Supabase client
    const supabase = await createClient();

    // Get the current user's ID for audit purposes
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: "Unauthorized. You must be logged in to generate a super license." },
        { status: 401 }
      );
    }

    const userId = user.id;

    // Set dates for the license
    const now = new Date();
    // Default to 12 months for GET method
    const expiryDate = new Date(now);
    expiryDate.setMonth(expiryDate.getMonth() + 12);

    // Insert the license into the database
    const { data, error } = await supabase
      .from('licenses')
      .insert({
        user_id: userId,
        license_key: licenseKey,
        status: 'ACTIVE',
        start_date: now.toISOString(),
        end_date: expiryDate.toISOString(),
        max_devices: 10, // Super licenses can be used on more devices
        is_super: true, // Set is_super to true as requested
        features: JSON.stringify({
          isPro: true,
          isEnterprise: true,
          allowedExports: ["pdf", "pptx", "html", "png", "svg"]
        })
      })
      .select()
      .single();

    if (error) {
      console.error("Error inserting super license:", error);
      return NextResponse.json(
        { error: "Failed to save super license key" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      licenseKey,
      id: data.id,
      created: data.created_at,
      expiry: data.end_date
    });
  } catch (error) {
    console.error("Error generating super license key:", error);
    return NextResponse.json(
      { error: "Failed to generate super license key" },
      { status: 500 }
    );
  }
}
