"use client";

import ErrorDisplay from "@/components/error-display";
import { useEffect } from "react";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <html lang="en">
      <body>
        <ErrorDisplay
          code="500"
          title="Oops!"
          message={error.message || "A critical error has occurred."}
          showHomeButton={true}
          showTryAgainButton={true}
          onTryAgain={reset}
        />
      </body>
    </html>
  );
}
