"use client";

import { useState, useEffect, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Copy, Key, Loader2, User, Search, X } from "lucide-react";
import { createClient } from "@/utils/supabase/client";

interface User {
  id: string;
  email: string;
}

export default function SuperLicense() {
  const [licenseKey, setLicenseKey] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [selectedUserEmail, setSelectedUserEmail] = useState<string>("");
  const [licenseMonths, setLicenseMonths] = useState<number>(12);

  const supabase = createClient();

  // Fetch users from the database
  useEffect(() => {
    async function fetchUsers() {
      setIsLoadingUsers(true);
      try {
        const { data, error } = await supabase
          .from("users")
          .select("id, email")
          .order("email");

        if (error) {
          throw error;
        }

        setUsers(data || []);
      } catch (error) {
        console.error("Error fetching users:", error);
        toast.message("Failed to load users", {
          description: "Please refresh the page and try again"
        });
      } finally {
        setIsLoadingUsers(false);
      }
    }

    fetchUsers();
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      const target = event.target as HTMLElement;
      if (!target.closest('#user-search-container') && showDropdown) {
        setShowDropdown(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDropdown]);

  // Filter users based on search query
  const filteredUsers = useMemo(() => {
    if (!searchQuery.trim()) {
      return users;
    }

    const query = searchQuery.toLowerCase().trim();
    return users.filter(user =>
      user.email.toLowerCase().includes(query)
    );
  }, [users, searchQuery]);

  // Handle user selection
  const handleSelectUser = (user: User) => {
    setSelectedUserId(user.id);
    setSelectedUserEmail(user.email);
    setSearchQuery("");
    setShowDropdown(false);
  };

  // Clear selected user
  const handleClearUser = () => {
    setSelectedUserId("");
    setSelectedUserEmail("");
    setSearchQuery("");
  };

  const handleGenerateLicense = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/generate-license', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: selectedUserId || '',
          months: licenseMonths
        }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      setLicenseKey(data.licenseKey);

      // Format the duration message
      let durationMessage = '';
      if (licenseMonths >= 12 && licenseMonths % 12 === 0) {
        const years = licenseMonths / 12;
        durationMessage = `${years} year${years > 1 ? 's' : ''}`;
      } else if (licenseMonths > 12) {
        const years = Math.floor(licenseMonths / 12);
        const months = licenseMonths % 12;
        durationMessage = `${years} year${years > 1 ? 's' : ''} and ${months} month${months > 1 ? 's' : ''}`;
      } else {
        durationMessage = `${licenseMonths} month${licenseMonths > 1 ? 's' : ''}`;
      }

      toast.message("Super license key generated successfully", {
        description: `Valid for ${durationMessage}`
      });
    } catch (error) {
      console.error("Error generating super license key:", error);
      toast.message("Failed to generate super license key", {
        description: "Please try again later"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyLicense = () => {
    if (!licenseKey) {
      toast.message("No license key to copy");
      return;
    }

    navigator.clipboard.writeText(licenseKey)
      .then(() => {
        setIsCopied(true);
        toast.message("License key copied to clipboard");

        // Reset the "Copied" state after 2 seconds
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch((error) => {
        console.error("Error copying license key:", error);
        toast.message("Failed to copy license key");
      });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Super License Generator</h2>
      </div>

      <Card className="p-6 bg-gray-50 border border-gray-200">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">License Settings</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User Selection */}
            <div className="space-y-2">
              <Label htmlFor="user-search">Select User (Optional)</Label>
              <div id="user-search-container" className="relative">
                {selectedUserId ? (
                  <div className="flex items-center justify-between w-full h-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white">
                    <span className="truncate">{users.find(u => u.id === selectedUserId)?.email}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleClearUser}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="user-search"
                        placeholder="Search users by email..."
                        className="w-full pl-9 pr-3"
                        value={searchQuery}
                        onChange={(e) => {
                          setSearchQuery(e.target.value);
                          setShowDropdown(true);
                        }}
                        onFocus={() => setShowDropdown(true)}
                        disabled={isLoadingUsers}
                      />
                    </div>

                    {showDropdown && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                        {isLoadingUsers ? (
                          <div className="p-2 text-center text-gray-500">Loading users...</div>
                        ) : filteredUsers.length > 0 ? (
                          <ul>
                            {filteredUsers.map((user) => (
                              <li
                                key={user.id}
                                className="px-3 py-2 hover:bg-gray-100 cursor-pointer truncate"
                                onClick={() => handleSelectUser(user)}
                              >
                                {user.email}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <div className="p-2 text-center text-gray-500">No users found</div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>

              <p className="text-sm text-gray-500">
                {isLoadingUsers ? "Loading users..." : users.length === 0 ? "No users found" : `${users.length} users available`}
              </p>
            </div>

            {/* License Duration */}
            <div className="space-y-2">
              <Label htmlFor="license-months">License Duration (Months)</Label>
              <div className="flex items-center gap-4">
                <Input
                  id="license-months"
                  type="number"
                  min="1"
                  max="240"
                  value={licenseMonths}
                  onChange={(e) => setLicenseMonths(Math.max(1, Math.min(240, parseInt(e.target.value) || 1)))}
                  className="w-full"
                />
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setLicenseMonths(Math.max(1, licenseMonths - 1))}
                    disabled={licenseMonths <= 1}
                    className="px-3"
                  >
                    -
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setLicenseMonths(Math.min(240, licenseMonths + 1))}
                    disabled={licenseMonths >= 240}
                    className="px-3"
                  >
                    +
                  </Button>
                </div>
              </div>
              <p className="text-sm text-gray-500">
                License will be valid until {(() => {
                  const date = new Date();
                  date.setMonth(date.getMonth() + licenseMonths);
                  return date.toLocaleDateString();
                })()}
              </p>
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <Button
              onClick={handleGenerateLicense}
              disabled={isGenerating}
              className="flex items-center gap-2"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Key className="h-4 w-4" />
                  Generate License Key
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-gray-50 border border-gray-200">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Generated Super License</h3>

          {licenseKey ? (
            <div className="flex items-center gap-4">
              <div className="flex-1 p-4 bg-white border border-gray-200 rounded-md font-mono text-sm break-all">
                {licenseKey}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyLicense}
                className="flex items-center gap-1.5"
              >
                <Copy className="h-4 w-4" />
                {isCopied ? "Copied!" : "Copy"}
              </Button>
            </div>
          ) : (
            <div className="p-4 bg-white border border-gray-200 rounded-md text-gray-500 text-center">
              No license key generated yet. Click the "Generate License Key" button to create one.
            </div>
          )}
        </div>
      </Card>

    </div>
  );
}
