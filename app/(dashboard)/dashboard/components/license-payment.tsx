"use client";

import { useEffect, useState, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaymentMethod {
  name: string;
  id: string;
}

interface User {
  id: string;
  full_name: string;
}

interface PaymentTransaction {
  id: string;
  amount: number;
  currency: string;
  status: string;
  completed_at: string;
  payment_methods: PaymentMethod;
  user: User;
}

interface PaymentRecord {
  id: string;
  date: string;
  amount: number;
  status: string;
  method: string;
  currency: string;
  payment_method_id: string;
  user_name: string;
}

const ITEMS_PER_PAGE = 10;

export default function LicensePayment() {
  const [payments, setPayments] = useState<PaymentRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const supabase = createClient();

  const fetchPayments = useCallback(async (page: number) => {
    try {
      setIsLoading(true);
      // 获取当前用户
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error("用户未登录");
      }

      // 获取总记录数
      const { count } = await supabase
        .from('payment_transactions')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);

      if (count !== null) {
        setTotalPages(Math.ceil(count / ITEMS_PER_PAGE));
      }

      // 联合查询 payment_transactions 和 payment_methods 表，添加分页
      const { data, error } = await supabase
        .from('payment_transactions')
        .select(`
          id,
          amount,
          currency,
          status,
          completed_at,
          payment_methods (
            name,
            id
          ),
          user:user_id (
            full_name
          )
        `)
        .eq('user_id', user.id)
        .order('completed_at', { ascending: false })
        .range((page - 1) * ITEMS_PER_PAGE, page * ITEMS_PER_PAGE - 1);

      if (error) {
        throw error;
      }

      // 转换数据格式
      const formattedPayments = (data as unknown as PaymentTransaction[]).map(payment => ({
        id: payment.id,
        date: new Date(payment.completed_at).toLocaleDateString(),
        amount: payment.amount,
        status: payment.status,
        method: payment.payment_methods?.name || 'Unknown',
        currency: payment.currency,
        payment_method_id: payment.payment_methods?.id,
        user_name: payment.user?.full_name || 'Unknown'
      }));

      setPayments(formattedPayments);
    } catch (err) {
      console.error('获取支付记录失败:', err);
      setError(err instanceof Error ? err.message : '获取支付记录失败');
    } finally {
      setIsLoading(false);
    }
  }, [supabase]);

  useEffect(() => {
    fetchPayments(currentPage);
  }, [currentPage, fetchPayments]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Payment Records</h2>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Payment Records</h2>
          <p className="text-muted-foreground text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Payment Records</h2>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full rounded-xl shadow-xl overflow-hidden">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-4 text-left">Date</th>
              <th className="px-6 py-4 text-left">User</th>
              <th className="px-6 py-4 text-left">Amount</th>
              <th className="px-6 py-4 text-left">Status</th>
              <th className="px-6 py-4 text-left">Method</th>
              <th className="px-6 py-4 text-left">Currency</th>
            </tr>
          </thead>
          <tbody>
            {payments.length === 0 ? (
              <tr>
                <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                  No payment records found
                </td>
              </tr>
            ) : (
              payments.map((p) => (
                <tr key={p.id} className="border-b last:border-0 hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4">{p.date}</td>
                  <td className="px-6 py-4">{p.user_name}</td>
                  <td className="px-6 py-4">${p.amount.toFixed(2)}</td>
                  <td className="px-6 py-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      p.status === 'completed' ? 'bg-green-100 text-green-800' :
                      p.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      p.status === 'failed' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {p.status}
                    </span>
                  </td>
                  <td className="px-6 py-4">{p.method}</td>
                  <td className="px-6 py-4">{p.currency}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-4">
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="flex items-center gap-2"
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}