"use client";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { fetchLicense } from "@/components/license-generator";
import { TexSlideLicense, TexSlideDevice } from "@/components/texslide-license";
import { CheckCircle, AlertCircle, XCircle } from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { useEffect, useState } from "react";
import { toast } from "sonner";


export default function LicenseInfo() {
  const [licenses, setLicenses] = useState<TexSlideLicense[]>([]);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    async function loadLicenseInfo() {
      try {
        // 获取当前用户
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError) throw userError;

        if (user) {
          // 获取用户的所有许可证
          const { data: licenseData, error: licenseError } = await supabase
            .from('licenses')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });

          if (licenseError) throw licenseError;

          if (licenseData && licenseData.length > 0) {
            // 并发获取所有 license 的详细信息和设备数量
            const licenseList = await Promise.all(
              licenseData.map(async (value) => {
                const license = await fetchLicense(user.id, value.id);
                // 获取该 license 的设备数量
                const { data: devices, error: deviceError } = await supabase
                  .from('devices')
                  .select('*')
                  .eq('license_id', value.id);
                
                if (!deviceError && devices) {
                  license.data.attributes.devices_count = devices.length;
                }
                return license;
              })
            );
            setLicenses(licenseList);
          }
        }
      } catch (error) {
        console.error('Error loading license info:', error);
        toast.error("Failed to load license information");
      } finally {
        setLoading(false);
      }
    }

    loadLicenseInfo();
  }, []);

  if (loading) {
    return <div>Loading license information...</div>;
  }

  if (licenses.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">License Information</h2>
          <p className="text-muted-foreground">No active licenses found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">License Information</h2>
        <p className="text-muted-foreground">View all your licenses</p>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full rounded-xl shadow-xl overflow-hidden">
          <thead className="bg-gray-100">
            <tr>
              <th className="px-6 py-4 text-left">Key</th>
              <th className="px-6 py-4 text-center">Status</th>
              <th className="px-6 py-4 text-center">Devices</th>
              <th className="px-6 py-4 text-left">Start</th>
              <th className="px-6 py-4 text-left">Expires</th>
            </tr>
          </thead>
          <tbody>
            {licenses.map((license, idx) => {
              let statusColor = "";
              let statusIcon = null;
              if (license.data.attributes.status === "ACTIVE") {
                statusColor = "text-green-700 bg-green-100";
                statusIcon = <CheckCircle className="inline-block mr-1 text-green-500" size={18} />;
              } else if (license.data.attributes.status === "EXPIRED") {
                statusColor = "text-yellow-800 bg-yellow-100";
                statusIcon = <AlertCircle className="inline-block mr-1 text-yellow-500" size={18} />;
              } else {
                statusColor = "text-red-700 bg-red-100";
                statusIcon = <XCircle className="inline-block mr-1 text-red-500" size={18} />;
              }
              return (
                <tr
                  key={license.data.attributes.key}
                  className={
                    `transition-all duration-200 ${idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50`}
                >
                  <td className="px-6 py-4 font-mono font-semibold text-base max-w-xs truncate rounded-l-lg">
                    {license.data.attributes.key}
                  </td>
                  <td className={`px-6 py-4 text-center`}>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full font-medium shadow-sm ${statusColor}`}>
                      {statusIcon}
                      {license.data.attributes.status.charAt(0).toUpperCase() + license.data.attributes.status.slice(1).toLowerCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-center">
                    <span className="inline-flex items-center px-3 py-1 rounded-full font-medium bg-blue-100 text-blue-700">
                      {license.data.attributes.devices_count}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-gray-700">
                    {new Date(license.data.attributes.created).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 text-gray-700 rounded-r-lg">
                    {new Date(license.data.attributes.expiry).toLocaleDateString()}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}