"use client";

import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { fetchLicense } from "@/components/license-generator";
import { TexSlideLicense, TexSlideDevice } from "@/components/texslide-license";
import { CheckCircle, AlertCircle, XCircle } from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { useEffect, useState, useCallback } from "react";
import { toast } from "sonner";

// 扩展 TexSlideLicense 类型以包含 licenseId
interface ExtendedLicense extends TexSlideLicense {
  licenseId: string;
}

export default function LicenseInfo() {
  const [licenses, setLicenses] = useState<ExtendedLicense[]>([]);
  const [licenseDevices, setLicenseDevices] = useState<{[key: string]: TexSlideDevice[]}>({});
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  // 使用 useCallback 缓存获取许可证的函数
  const getLicenseWithDevices = useCallback(async (userId: string, licenseId: string) => {
    try {
      // 并行获取许可证和设备信息
      const [license, { data: devices }] = await Promise.all([
        fetchLicense(userId, licenseId),
        supabase
          .from('devices')
          .select('*')
          .eq('license_id', licenseId)
      ]);

      return {
        license: { ...license, licenseId } as ExtendedLicense,
        devices: devices || []
      };
    } catch (error) {
      console.error(`Error fetching license ${licenseId}:`, error);
      return null;
    }
  }, [supabase]);

  useEffect(() => {
    async function loadLicenseInfo() {
      try {
        // 获取当前用户
        const { data: { user }, error: userError } = await supabase.auth.getUser();
        if (userError) throw userError;

        if (user) {
          // 获取用户的所有许可证
          const { data: licenseData, error: licenseError } = await supabase
            .from('licenses')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });

          if (licenseError) throw licenseError;

          if (licenseData && licenseData.length > 0) {
            // 并行获取所有许可证和设备信息
            const results = await Promise.all(
              licenseData.map(license => getLicenseWithDevices(user.id, license.id))
            );

            // 过滤掉获取失败的结果并更新状态
            const validResults = results.filter((result): result is NonNullable<typeof result> => result !== null);
            
            setLicenses(validResults.map(result => result.license));
            setLicenseDevices(
              validResults.reduce((acc, { license, devices }) => ({
                ...acc,
                [license.licenseId]: devices
              }), {})
            );
          }
        }
      } catch (error) {
        console.error('Error loading license info:', error);
        toast.error("Failed to load license information");
      } finally {
        setLoading(false);
      }
    }

    loadLicenseInfo();
  }, [getLicenseWithDevices]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
      </div>
    );
  }

  if (licenses.length === 0) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">License Information</h2>
          <p className="text-muted-foreground">No active licenses found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold">License Information</h2>
      </div>

      {licenses.map((license, index) => {
        const licenseId = license.licenseId;
        const devices = licenseDevices[licenseId] || [];
        const devicesUsed = devices.length;
        const isExpired = new Date(license.data.attributes.expiry) < new Date();

        return (
          <div key={licenseId} className="border-b pb-8 mb-8 last:border-0 last:pb-0 last:mb-0">
            <h3 className="text-xl font-semibold mb-4">
              License {index + 1}
              {index === 0 && <Badge className="ml-2 bg-blue-500">Latest</Badge>}
            </h3>

            <div className="grid gap-6 md:grid-cols-3">
              <Card className="p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <h3>Key</h3>
                  <Badge variant="outline" className="font-mono">
                    {license.data.attributes.key}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Status</span>
                  <div className="flex items-center gap-2">
                    {license.data.attributes.status === 'ACTIVE' ? (
                      <>
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Active
                      </>
                    ) : license.data.attributes.status === 'EXPIRED' ? (
                      <>
                        <XCircle className="h-4 w-4 text-yellow-500" />
                        Expired
                      </>
                    ) : (
                      <>
                        <XCircle className="h-4 w-4 text-red-500" />
                        Revoked
                      </>
                    )}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span>Start Date</span>
                  <span>{new Date(license.data.attributes.created).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Expiration Date</span>
                  <span>{new Date(license.data.attributes.expiry).toLocaleDateString()}</span>
                </div>
              </Card>

              <Card className="p-6 space-y-4 md:col-span-2">
                <div>
                  <h3 className="text-lg font-semibold">Device Usage</h3>
                  <p className="text-sm text-muted-foreground">
                    {devicesUsed} of {2} devices used
                  </p>
                </div>
                <Progress
                  value={(devicesUsed / 2) * 100}
                  className={isExpired ? "bg-yellow-200" : ""}
                />
                {devicesUsed >= 2 && (
                  <div className="flex items-center gap-2 text-sm text-yellow-600">
                    <AlertCircle className="h-4 w-4" />
                    You have reached your device limit
                  </div>
                )}
                {isExpired && (
                  <div className="flex items-center gap-2 text-sm text-yellow-600">
                    <AlertCircle className="h-4 w-4" />
                    Your license has expired
                  </div>
                )}

                {devices.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-semibold mb-2">Connected Devices</h4>
                    <div className="space-y-2">
                      <div className="grid grid-cols-12 gap-4 text-xs font-medium text-muted-foreground mb-2 bg-gray-100 p-2 rounded-lg">
                        <div className="col-span-5">Device ID</div>
                        <div className="col-span-4">Device Name</div>
                        <div className="col-span-3">Created</div>
                      </div>
                      {devices.map((device) => (
                        <div key={device.device_id} className="grid grid-cols-12 gap-4 text-sm p-2 rounded-lg bg-gray-50">
                          <div className="col-span-5 font-mono text-xs truncate" title={device.device_id}>
                            {device.device_id}
                          </div>
                          <div className="col-span-4 truncate" title={device.device_name || 'Unnamed Device'}>
                            {device.device_name || 'Unnamed Device'}
                          </div>
                          <div className="col-span-3 text-muted-foreground">
                            {new Date(device.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>
        );
      })}
    </div>
  );
}