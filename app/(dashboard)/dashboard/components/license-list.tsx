"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Download, Search, RefreshCw, Copy } from "lucide-react";

type License = {
  id: string;
  user_id: string;
  license_key: string;
  status: 'ACTIVE' | 'EXPIRED' | 'REVOKED';
  start_date: string;
  end_date: string;
  created_at: string;
  updated_at: string;
  max_devices: number;
  features: any;
  is_super: boolean;
  user_email?: string;
};

export default function LicenseList() {
  const [licenses, setLicenses] = useState<License[]>([]);
  const [filteredLicenses, setFilteredLicenses] = useState<License[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  // Always show full license keys
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const supabase = createClient();

  // Load licenses
  useEffect(() => {
    async function loadLicenses() {
      setLoading(true);
      try {
        // First get all licenses
        const { data: licensesData, error: licensesError } = await supabase
          .from("licenses")
          .select("*")
          .order("created_at", { ascending: false });

        if (licensesError) throw licensesError;

        // Get user emails for each license
        const userIds = [...new Set(licensesData?.map(license => license.user_id) || [])];
        const { data: usersData, error: usersError } = await supabase
          .from("users")
          .select("id, email")
          .in("id", userIds);

        if (usersError) throw usersError;

        // Create a map of user IDs to emails
        const userEmailMap = (usersData || []).reduce((map, user) => {
          map[user.id] = user.email;
          return map;
        }, {} as Record<string, string>);

        // Combine license data with user emails
        const licensesWithUserEmails = (licensesData || []).map(license => ({
          ...license,
          user_email: userEmailMap[license.user_id] || 'Unknown'
        }));

        setLicenses(licensesWithUserEmails || []);
        setFilteredLicenses(licensesWithUserEmails || []);
      } catch (error) {
        console.error("Error loading licenses:", error);
        toast.error("Failed to load licenses");
      } finally {
        setLoading(false);
      }
    }

    loadLicenses();
  }, []);

  // Apply filters
  useEffect(() => {
    let result = [...licenses];

    // Filter by status
    if (statusFilter) {
      result = result.filter((license) => license.status === statusFilter);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(
        (license) =>
          license.license_key?.toLowerCase().includes(term) ||
          license.user_email?.toLowerCase().includes(term)
      );
    }

    setFilteredLicenses(result);
  }, [statusFilter, searchTerm, licenses]);

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  // Handle copy license key
  const handleCopyLicenseKey = (licenseKey: string, id: string) => {
    navigator.clipboard.writeText(licenseKey)
      .then(() => {
        setCopiedId(id);
        toast.success("License key copied to clipboard");

        // Reset the "Copied" state after 2 seconds
        setTimeout(() => {
          setCopiedId(null);
        }, 2000);
      })
      .catch((error) => {
        console.error("Error copying license key:", error);
        toast.error("Failed to copy license key");
      });
  };

  // Export to CSV
  const exportToCSV = () => {
    if (filteredLicenses.length === 0) {
      toast.error("No licenses to export");
      return;
    }

    // Create CSV content
    const headers = ["User Email", "License Key", "Status", "Super License", "Start Date", "End Date", "Max Devices", "Created At"];
    const csvContent = [
      headers.join(","),
      ...filteredLicenses.map((license) =>
        [
          `"${license.user_email || ""}"`,
          `"${license.license_key || ""}"`,
          license.status,
          license.is_super ? "Yes" : "No",
          formatDate(license.start_date),
          formatDate(license.end_date),
          license.max_devices,
          formatDate(license.created_at),
        ].join(",")
      ),
    ].join("\n");

    // Create and download the file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0]; // YYYY-MM-DD format
    link.setAttribute("download", `licenses_${dateStr}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="search">Search</Label>
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              id="search"
              placeholder="Search by license key or email"
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div>
          <Label htmlFor="status">Status</Label>
          <select
            id="status"
            className="w-full h-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-primary-blue"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="">All statuses</option>
            <option value="ACTIVE">Active</option>
            <option value="EXPIRED">Expired</option>
            <option value="REVOKED">Revoked</option>
          </select>
        </div>
        <div className="flex items-end">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setSearchTerm("");
              setStatusFilter("");
            }}
            className="flex items-center gap-1 mr-2"
          >
            <RefreshCw className="h-4 w-4" />
            Reset Filters
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={exportToCSV}
            className="flex items-center gap-1"
          >
            <Download className="h-4 w-4" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* License Count */}
      <div className="flex justify-end items-center">
        <span className="text-sm text-gray-500">
          {filteredLicenses.length} licenses found
        </span>
      </div>

      {/* Licenses Table */}
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User Email</TableHead>
              <TableHead>
                License Key
                <span className="ml-1 text-xs text-gray-500 font-normal">(click to copy)</span>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Super License</TableHead>
              <TableHead>Start Date</TableHead>
              <TableHead>End Date</TableHead>
              <TableHead>Max Devices</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  Loading licenses...
                </TableCell>
              </TableRow>
            ) : filteredLicenses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  No licenses found
                </TableCell>
              </TableRow>
            ) : (
              filteredLicenses.map((license) => (
                <TableRow key={license.id}>
                  <TableCell>{license.user_email || "—"}</TableCell>
                  <TableCell
                    className="font-mono text-xs cursor-pointer hover:bg-gray-50"
                    onClick={() => handleCopyLicenseKey(license.license_key, license.id)}
                    title="Click to copy license key"
                  >
                    {copiedId === license.id ? (
                      <span className="text-green-600 flex items-center">
                        <Copy className="h-3.5 w-3.5 mr-1" />
                        Copied!
                      </span>
                    ) : (
                      <>
                        {license.license_key}
                      </>
                    )}
                  </TableCell>
                  <TableCell>
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        license.status === "ACTIVE"
                          ? "bg-green-100 text-green-800"
                          : license.status === "EXPIRED"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {license.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    {license.is_super ? (
                      <span className="px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                        Super
                      </span>
                    ) : (
                      <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                        Standard
                      </span>
                    )}
                  </TableCell>
                  <TableCell>{formatDate(license.start_date)}</TableCell>
                  <TableCell>{formatDate(license.end_date)}</TableCell>
                  <TableCell className="text-center">{license.max_devices}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
