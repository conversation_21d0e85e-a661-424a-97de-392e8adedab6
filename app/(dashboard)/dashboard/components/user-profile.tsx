"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";

export default function UserProfile() {
  const [formData, setFormData] = useState({
    full_name: "",
    email: ""
  });
  const [loading, setLoading] = useState(false);
  const [sendingResetEmail, setSendingResetEmail] = useState(false);
  const supabase = createClient();

  // 从 session 和数据库加载用户数据
  useEffect(() => {
    async function loadUserProfile() {
      try {
        // 首先尝试从 session 获取用户信息
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        if (sessionError) throw sessionError;

        if (session?.user) {
          const userData = session.user.user_metadata;

          // 如果 session 中有完整的用户信息，直接使用
          if (userData?.full_name && userData?.email) {
            setFormData({
              full_name: userData.full_name,
              email: userData.email
            });
            return;
          }

          // 如果 session 中的信息不完整，从数据库获取
          const { data, error } = await supabase
            .from('users')
            .select('full_name, email')
            .eq('id', session.user.id)
            .single();

          if (error) throw error;

          if (data) {
            // 如果 full_name 为空，使用 email 的 @ 前面部分作为默认名称
            let fullName = data.full_name;
            const email = data.email || "";

            if (!fullName || fullName.trim() === "") {
              // 从邮箱中提取用户名部分（@ 前面的部分）
              const atIndex = email.indexOf('@');
              if (atIndex > 0) {
                fullName = email.substring(0, atIndex);
                console.log("Using email username as default full name:", fullName);
              }
            }

            setFormData({
              full_name: fullName || "",
              email: email
            });
          }
        }
      } catch (error) {
        console.error('Error loading user profile:', error);
        toast.error("Failed to load user profile");
      }
    }

    loadUserProfile();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // 获取当前用户
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;

      if (user) {
        // 如果 full_name 为空，使用 email 的 @ 前面部分作为默认名称
        let fullName = formData.full_name;

        if (!fullName || fullName.trim() === "") {
          // 从邮箱中提取用户名部分（@ 前面的部分）
          const atIndex = formData.email.indexOf('@');
          if (atIndex > 0) {
            fullName = formData.email.substring(0, atIndex);
            console.log("Using email username as default full name for update:", fullName);
            // 更新表单数据，这样用户界面也会显示这个默认名称
            setFormData(prev => ({ ...prev, full_name: fullName }));
          }
        }

        // 更新用户信息
        const updateData: any = {
          full_name: fullName,
          updated_at: new Date().toISOString()
        };

        // 更新用户信息
        const { error } = await supabase
          .from('users')
          .update(updateData)
          .eq('id', user.id);

        if (error) throw error;

        // 更新 session 中的用户数据
        const { error: updateSessionError } = await supabase.auth.updateUser({
          data: {
            full_name: fullName
          }
        });

        if (updateSessionError) throw updateSessionError;

        toast.success("Profile updated successfully");
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error("Failed to update profile");
    } finally {
      setLoading(false);
    }
  };

  // Function to send password reset email
  const handleResetPassword = async () => {
    if (!formData.email) {
      toast.error("Email is required", {
        duration: 5000, // Show for 5 seconds
        style: {
          fontSize: '16px',
          padding: '16px',
          maxWidth: '500px'
        }
      });
      return;
    }

    setSendingResetEmail(true);
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(formData.email, {
        redirectTo: `${window.location.origin}/auth/callback?redirect_to=/reset-password`,
      });

      if (error) {
        console.error('Supabase returned an error:', error);
        throw error;
      }

      toast.success("Please check your email for a link to reset your password.", {
        duration: 5000, // Show for 5 seconds
        style: {
          fontSize: '16px',
          padding: '16px',
          maxWidth: '500px'
        }
      });
    } catch (error) {
      console.error('Error sending password reset email:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
      toast.error("Failed to send password reset email", {
        duration: 5000, // Show for 5 seconds
        style: {
          fontSize: '16px',
          padding: '16px',
          maxWidth: '500px'
        }
      });
    } finally {
      setSendingResetEmail(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Personal Information</h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="flex items-end gap-4">
          <div className="flex-1 space-y-2">
            <Label htmlFor="full_name">Full Name</Label>
            <Input
              id="full_name"
              value={formData.full_name}
              onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
            />
          </div>
          <div className="flex-1 space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              readOnly
              disabled
              className="bg-gray-100 cursor-not-allowed"
            />
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleResetPassword}
            disabled={sendingResetEmail}
            className="h-10"
          >
            {sendingResetEmail ? "Sending..." : "Change Password"}
          </Button>
        </div>
        <div>
          <Button type="submit" disabled={loading}>
            {loading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </div>
  );
}