"use client";

import ErrorDisplay from "@/components/error-display";
import { useSearchParams } from "next/navigation";
import { signOutAction } from "@/app/actions";
import { useCallback } from "react";

export default function ErrorPage() {
  const searchParams = useSearchParams();
  const errorMessage = searchParams.get("message") || "An unexpected error occurred";

  const handleBackToHome = useCallback(async () => {
    // Sign out the user and redirect to homepage
    await signOutAction();
  }, []);

  return (
    <ErrorDisplay
      code="Error"
      title="Oops!"
      message={errorMessage}
      showHomeButton={true}
      onHomeButtonClick={handleBackToHome}
    />
  );
}
