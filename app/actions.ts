"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

export const signUpAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const password = formData.get("password")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");

  if (!email || !password) {
    return {
      success: false,
      message: {"error" : "Email and password are required"}
    }
  }

  // 如果邮箱已存在，返回错误
  const { data: existingUser, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('email', email)
    .single();

  if (userError && userError.code !== 'PGRST116') {
    console.error('Error checking user existence:', userError);
    return {
      success: false,
      message: {"error" : "An error occurred while checking user existence"}
    };
  }

  if (existingUser) {
    return {
      success: false,
      message: {"error" : "<PERSON><PERSON> already exists"}
    };
  }

  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback?signup=true`,
    },
  });

  if (error) {
    console.log("error: ", error);
    return {
      success: false,
      message: {"error" : error.message}
    }
  }

  console.log("data: ", data);

  return {
    success: true,
    message: {"success" : "Thanks for signing up! Please check your email for a verification link."}
  };

};

export const signInWithEmail = async (formData: FormData) => {
  const email = formData.get("email") as string;
  const userRegion = formData.get("userRegion") as string;
  const supabase = await createClient();

  try {
    const { data, error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
      },
    });

    if (error) {
      throw error;
    }

    return {
      success: true
    };
  } catch (error) {
    return { 
      data: null, 
      error: error instanceof Error ? error.message : 'An error occurred during GitHub authentication' 
    };
  }
};

export const signInWithGithub = async () => {
  const supabase = createClient();
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'github',
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback?github=true`,
      }
    });
    
    if (error) {
      throw error;
    }
    
    return { data, error: null };
  } catch (error) {
    return { 
      data: null, 
      error: error instanceof Error ? error.message : 'An error occurred during GitHub authentication' 
    };
  }
};

export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email is required");
  }

  // Check if the email exists in the users table
  const { data: existingUser, error: userError } = await supabase
    .from('users')
    .select('email')
    .eq('email', email)
    .single();

  if (userError && userError.code !== 'PGRST116') {
    console.error('Error checking user existence:', userError);
  }

  if (!existingUser) {
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Account with this email not found."
    );
  }

  // If the email exists, proceed with password reset
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/callback?redirect_to=/reset-password`,
  });

  if (error) {
    console.error(error.message);
    return encodedRedirect(
      "error",
      "/forgot-password",
      "Could not reset password",
    );
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect(
    "success",
    "/forgot-password",
    "Check your email for a link to reset your password.",
  );
};

export const resetPasswordAction = async (formData: FormData) => {
  const supabase = await createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    encodedRedirect(
      "error",
      "/reset-password",
      "Password and confirm password are required",
    );
  }

  if (password !== confirmPassword) {
    encodedRedirect(
      "error",
      "/reset-password",
      "Passwords do not match",
    );
  }

  const { error } = await supabase.auth.updateUser({
    password: password,
  });

  if (error) {
    encodedRedirect(
      "error",
      "/reset-password",
      "Password update failed",
    );
  }

  encodedRedirect("success", "/reset-password", "Password updated");
};

export const signOutAction = async () => {
  const supabase = await createClient();
  await supabase.auth.signOut();
  return redirect("/");
};
