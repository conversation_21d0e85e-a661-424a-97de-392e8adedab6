"use client";

import ErrorDisplay from "@/components/error-display";
import { useEffect } from "react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <ErrorDisplay
      code="Error"
      title="Oops!"
      message={error.message || "Something went wrong."}
      showHomeButton={true}
      showTryAgainButton={true}
      onTryAgain={reset}
    />
  );
}
