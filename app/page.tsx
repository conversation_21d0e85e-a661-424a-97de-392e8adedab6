import Hero from "@/components/hero";
import ConnectSupabaseSteps from "@/components/tutorial/connect-supabase-steps";
import SignUpUserSteps from "@/components/tutorial/sign-up-user-steps";
import { hasEnvVars } from "@/utils/supabase/check-env-vars";
import GetStartedButton from "@/components/get-started-button";

export default function Home() {
  return (
    <>
      <nav className="navbar fixed w-full z-10 py-4">
        <div className="container mx-auto flex justify-between items-center px-6">
          <div className="flex items-center">
            <span className="text-2xl font-bold text-gray-800">
              <span style={{ color: 'var(--primary-blue)' }}>Tex</span>Slide
            </span>
          </div>
          <div className="hidden md:flex space-x-8">
            <a href="#features" className="text-gray-600 hover:text-gray-900">Features</a>
            <a href="#benefits" className="text-gray-600 hover:text-gray-900">Benefits</a>
            <a href="#download" className="text-gray-600 hover:text-gray-900">Download</a>
          </div>
          <GetStartedButton />
        </div>
      </nav>

      {/* Hero Section */}
      <section className="hero-bg pt-32 pb-20">
        <div className="container mx-auto px-6">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 md:pr-10">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                <span style={{ color: 'var(--primary-blue)' }}>TexSlide:</span> Designed for Scientific Presentations
              </h1>
              <p className="text-lg mb-8">
                TexSlide is presentation software deeply customized based on LibreOffice Impress, designed for researchers, academics, and engineers requiring precise presentation of complex mathematical formulas. It deeply integrates your local LaTeX environment, allowing you to easily leverage LaTeX's powerful formula typesetting and custom macro support within a familiar slide editing interface.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <GetStartedButton />
                <button className="border border-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-50">
                  Learn More
                </button>
              </div>
            </div>
            <div className="md:w-1/2 mt-10 md:mt-0">
              <div className="bg-white p-4 rounded-lg shadow-xl">
                <div className="p-4 bg-gray-50 rounded border border-gray-200">
                  <div className="font-serif italic text-xl math-display">
                    {'$$f(x) = \\int_{-\\infty}^{\\infty} \\hat{f}(\\xi) e^{2\\pi i \\xi x} d\\xi$$'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Highlights */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-semibold text-center mb-16 section-title">Core Highlights</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="feature-card text-center">
              <i className="fas fa-desktop feature-icon"></i>
              <h3 className="text-xl font-semibold mb-3">Familiar Creation</h3>
              <p>Built on LibreOffice Impress for easy adoption, ensuring you can start creating professional presentations immediately with minimal learning curve.</p>
            </div>

            <div className="feature-card text-center">
              <i className="fas fa-square-root-alt feature-icon"></i>
              <h3 className="text-xl font-semibold mb-3">Full LaTeX Power</h3>
              <p>Supports all formula syntax and custom macros, bringing the complete typesetting capabilities of LaTeX to your presentations.</p>
            </div>

            <div className="feature-card text-center">
              <i className="fas fa-align-left feature-icon"></i>
              <h3 className="text-xl font-semibold mb-3">Elevated Formula Display</h3>
              <p>Featuring revolutionary inline math integration, allowing formulas to seamlessly blend with text for professional-looking slides.</p>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
