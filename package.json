{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.9", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "autoprefixer": "10.4.20", "base64-arraybuffer": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "next": "latest", "next-themes": "^0.4.3", "prettier": "^3.3.3", "react": "19.0.0", "react-dom": "19.0.0", "react-google-recaptcha": "^3.1.0", "sonner": "^2.0.3"}, "devDependencies": {"@types/node": "22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "19.0.2", "@types/react-google-recaptcha": "^2.1.9", "postcss": "8.4.49", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2"}}