import fs from 'fs';
import path from 'path';

interface MagicLinkEmailData {
  userEmail: string;
  magicLinkUrl: string;
}

/**
 * Generate magic link email HTML content
 * @param data - Email data containing user email and magic link URL
 * @returns HTML string for the email
 */
export function generateMagicLinkEmail(data: MagicLinkEmailData): string {
  try {
    // Read the HTML template
    const templatePath = path.join(process.cwd(), 'email-templates', 'magic-link.html');
    let htmlTemplate = fs.readFileSync(templatePath, 'utf-8');

    // Replace placeholders with actual data
    htmlTemplate = htmlTemplate
      .replace(/\{\{MAGIC_LINK_URL\}\}/g, data.magicLinkUrl)
      .replace(/\{\{USER_EMAIL\}\}/g, data.userEmail);

    return htmlTemplate;
  } catch (error) {
    console.error('Error generating magic link email:', error);
    
    // Fallback to a simple HTML email if template reading fails
    return generateFallbackMagicLinkEmail(data);
  }
}

/**
 * Fallback email template in case the HTML file cannot be read
 */
function generateFallbackMagicLinkEmail(data: MagicLinkEmailData): string {
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>TexSlide - Sign In</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; }
            .header { text-align: center; margin-bottom: 30px; }
            .title { color: #0064c8; font-size: 24px; margin-bottom: 10px; }
            .tagline { color: #666; font-size: 16px; margin-bottom: 30px; }
            .button { display: inline-block; background: #0064c8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; }
            .link { word-break: break-all; color: #0064c8; margin-top: 20px; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1 class="title">TexSlide</h1>
                <p class="tagline">Present Your Math Flawlessly</p>
            </div>
            
            <h2>Sign in to your TexSlide account</h2>
            <p>Click the button below to securely sign in to your account. This link will expire in 1 hour.</p>
            
            <p style="text-align: center; margin: 30px 0;">
                <a href="${data.magicLinkUrl}" class="button">Sign In to TexSlide</a>
            </p>
            
            <p><strong>Link not working?</strong> Copy and paste this URL into your browser:</p>
            <p class="link">${data.magicLinkUrl}</p>
            
            <div class="footer">
                <p><strong>Security Notice:</strong> This email was sent to ${data.userEmail}. If you didn't request this sign-in link, you can safely ignore this email.</p>
            </div>
        </div>
    </body>
    </html>
  `;
}

/**
 * Generate plain text version of the magic link email
 * @param data - Email data containing user email and magic link URL
 * @returns Plain text string for the email
 */
export function generateMagicLinkEmailText(data: MagicLinkEmailData): string {
  return `
TexSlide - Sign In
Present Your Math Flawlessly

Sign in to your TexSlide account

Click the link below to securely sign in to your account. This link will expire in 1 hour for your security.

${data.magicLinkUrl}

Security Notice: This email was sent to ${data.userEmail}. If you didn't request this sign-in link, you can safely ignore this email. The link will expire automatically.

---
TexSlide Team
  `.trim();
}

/**
 * Email configuration for magic link emails
 */
export const MAGIC_LINK_EMAIL_CONFIG = {
  subject: 'Sign in to TexSlide',
  fromName: 'TexSlide',
  fromEmail: '<EMAIL>', // Replace with your actual domain
} as const;
