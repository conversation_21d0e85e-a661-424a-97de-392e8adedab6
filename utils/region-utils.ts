"use client";

/**
 * Detects the user's region using HTML5 Geolocation and reverse geocoding
 * @returns Promise<string | null> - Returns the country code (lowercase) or null if detection fails
 */
export async function detectUserRegion(): Promise<string | null> {
  try {
    // Default to null in case geolocation fails
    let region = null;

    // Check if geolocation is available
    if (typeof navigator !== 'undefined' && navigator.geolocation) {
      try {
        const position = await new Promise<GeolocationPosition>((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 0
          });
        });

        // Use reverse geocoding to get country
        const { latitude, longitude } = position.coords;

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);  // Fetch timeout
        
        try {
          // Fetch reverse geocoding data
          const response = await fetch(
            `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`,
            {
              headers: { 'User-Agent': navigator.userAgent || 'Unknown' },
              signal: controller.signal
            }
          );
          
          clearTimeout(timeoutId);
          
          if (!response.ok) {
            console.error("Geocoding failed with status:", response.status);
            return null;
          }

          const data = await response.json();
          return data.address?.country_code?.toLowerCase() || null;
          
        } catch (error) {
          clearTimeout(timeoutId);
          console.error("Reverse geocoding error:", error);
          return null;
        }
        
      } catch (error) {
        console.error("Geolocation error:", error);
      }
    } else {
      console.log("Geolocation is not supported by this browser");
    }

    return region;
  } catch (error) {
    console.error("Error detecting region:", error);
    return null;
  }
}

/**
 * Gets the appropriate currency symbol based on the region
 * @param region - The region code (e.g., 'cn', 'us')
 * @returns The currency symbol ('¥' for China, '$' for others)
 */
export function getCurrencySymbol(region: string | null): string {
  if (region === 'cn' || region === 'china') {
    return '¥';
  }
  return '$';
}

/**
 * Gets the currency code based on the region
 * @param region - The region code (e.g., 'cn', 'us')
 * @returns The currency code ('CNY' for China, 'USD' for others)
 */
export function getCurrencyCode(region: string | null): string {
  if (region === 'cn' || region === 'china') {
    return 'CNY';
  }
  return 'USD';
}
