import { createClient } from '@/utils/supabase/server';
import { generateMagicLinkEmail, generateMagicLinkEmailText, MAGIC_LINK_EMAIL_CONFIG } from './email-templates';

interface SendMagicLinkOptions {
  email: string;
  redirectTo?: string;
}

/**
 * Send a magic link email using Supabase Auth with custom email template
 * @param options - Email and redirect options
 * @returns Promise with success/error result
 */
export async function sendMagicLinkEmail(options: SendMagicLinkOptions) {
  try {
    const supabase = await createClient();
    const { email, redirectTo } = options;

    // Generate the magic link using Supabase
    const { data, error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        emailRedirectTo: redirectTo || `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
        // You can customize the email template here if your Supabase project supports it
        data: {
          // Custom data that can be used in email templates
          app_name: 'TexSlide',
          tagline: 'Present Your Math Flawlessly',
        }
      },
    });

    if (error) {
      throw error;
    }

    return {
      success: true,
      message: 'Magic link sent successfully',
      data
    };

  } catch (error) {
    console.error('Error sending magic link:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send magic link',
      data: null
    };
  }
}

/**
 * Alternative implementation using a custom email service
 * This would be used if you want to send emails through your own email service
 * instead of relying on Supabase's default email templates
 */
export async function sendCustomMagicLinkEmail(options: SendMagicLinkOptions & { magicLinkUrl: string }) {
  try {
    const { email, magicLinkUrl } = options;

    // Generate the email content
    const htmlContent = generateMagicLinkEmail({
      userEmail: email,
      magicLinkUrl: magicLinkUrl
    });

    const textContent = generateMagicLinkEmailText({
      userEmail: email,
      magicLinkUrl: magicLinkUrl
    });

    // Here you would integrate with your email service (e.g., SendGrid, Mailgun, etc.)
    // Example with a hypothetical email service:
    /*
    const emailService = new EmailService();
    await emailService.send({
      to: email,
      from: {
        email: MAGIC_LINK_EMAIL_CONFIG.fromEmail,
        name: MAGIC_LINK_EMAIL_CONFIG.fromName
      },
      subject: MAGIC_LINK_EMAIL_CONFIG.subject,
      html: htmlContent,
      text: textContent
    });
    */

    console.log('Custom magic link email would be sent to:', email);
    console.log('HTML Content:', htmlContent);
    console.log('Text Content:', textContent);

    return {
      success: true,
      message: 'Custom magic link email sent successfully'
    };

  } catch (error) {
    console.error('Error sending custom magic link email:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to send custom magic link email'
    };
  }
}

/**
 * Example of how to integrate with popular email services
 */

// Example with SendGrid
/*
import sgMail from '@sendgrid/mail';

export async function sendMagicLinkWithSendGrid(options: SendMagicLinkOptions & { magicLinkUrl: string }) {
  sgMail.setApiKey(process.env.SENDGRID_API_KEY!);

  const { email, magicLinkUrl } = options;
  
  const htmlContent = generateMagicLinkEmail({
    userEmail: email,
    magicLinkUrl: magicLinkUrl
  });

  const msg = {
    to: email,
    from: {
      email: MAGIC_LINK_EMAIL_CONFIG.fromEmail,
      name: MAGIC_LINK_EMAIL_CONFIG.fromName
    },
    subject: MAGIC_LINK_EMAIL_CONFIG.subject,
    html: htmlContent,
    text: generateMagicLinkEmailText({ userEmail: email, magicLinkUrl })
  };

  try {
    await sgMail.send(msg);
    return { success: true, message: 'Magic link sent via SendGrid' };
  } catch (error) {
    console.error('SendGrid error:', error);
    return { success: false, error: 'Failed to send email via SendGrid' };
  }
}
*/

// Example with Resend
/*
import { Resend } from 'resend';

export async function sendMagicLinkWithResend(options: SendMagicLinkOptions & { magicLinkUrl: string }) {
  const resend = new Resend(process.env.RESEND_API_KEY);

  const { email, magicLinkUrl } = options;
  
  const htmlContent = generateMagicLinkEmail({
    userEmail: email,
    magicLinkUrl: magicLinkUrl
  });

  try {
    const { data, error } = await resend.emails.send({
      from: `${MAGIC_LINK_EMAIL_CONFIG.fromName} <${MAGIC_LINK_EMAIL_CONFIG.fromEmail}>`,
      to: [email],
      subject: MAGIC_LINK_EMAIL_CONFIG.subject,
      html: htmlContent,
      text: generateMagicLinkEmailText({ userEmail: email, magicLinkUrl })
    });

    if (error) {
      throw error;
    }

    return { success: true, message: 'Magic link sent via Resend', data };
  } catch (error) {
    console.error('Resend error:', error);
    return { success: false, error: 'Failed to send email via Resend' };
  }
}
*/
