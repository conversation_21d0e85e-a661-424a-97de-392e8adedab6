-- Create users table
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE,
    full_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    last_login_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true NOT NULL,
    UNIQUE(oauth_provider, oauth_provider_id)
);

-- Create payment_methods table
CREATE TABLE IF NOT EXISTS public.payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    code TEXT NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT true NOT NULL,
    config J<PERSON><PERSON><PERSON> DEFAULT '{}'::jsonb NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create payment_transactions table
CREATE TABLE IF NOT EXISTS public.payment_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    payment_method_id UUID NOT NULL REFERENCES public.payment_methods(id),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    external_transaction_id TEXT,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'refunded', 'cancelled')),
    payment_data JSONB DEFAULT '{}'::jsonb NOT NULL,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create licenses table
CREATE TABLE IF NOT EXISTS public.licenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    license_key TEXT UNIQUE NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('ACTIVE', 'EXPIRED', 'REVOKED')),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    max_devices INTEGER DEFAULT 2 NOT NULL,
    is_super BOOLEAN DEFAULT false NOT NULL,
    features JSONB DEFAULT '{}'::jsonb NOT NULL
);

-- Create devices table
CREATE TABLE IF NOT EXISTS public.devices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    license_id UUID NOT NULL REFERENCES public.licenses(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL,
    device_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    UNIQUE(license_id, device_id)
);

-- Create license_payments table
CREATE TABLE IF NOT EXISTS public.license_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    license_id UUID NOT NULL REFERENCES public.licenses(id) ON DELETE CASCADE,
    payment_transaction_id UUID NOT NULL REFERENCES public.payment_transactions(id),
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    payment_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_payment_methods_code ON public.payment_methods(code);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_external_id ON public.payment_transactions(external_transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_status ON public.payment_transactions(status);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_user_id ON public.payment_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_licenses_user_id ON public.licenses(user_id);
CREATE INDEX IF NOT EXISTS idx_licenses_status ON public.licenses(status);
CREATE INDEX IF NOT EXISTS idx_licenses_end_date ON public.licenses(end_date);
CREATE INDEX IF NOT EXISTS idx_devices_license_id ON public.devices(license_id);
CREATE INDEX IF NOT EXISTS idx_devices_device_id ON public.devices(device_id);
CREATE INDEX IF NOT EXISTS idx_license_payments_license_id ON public.license_payments(license_id);
CREATE INDEX IF NOT EXISTS idx_license_payments_status ON public.license_payments(status);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE OR REPLACE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_payment_methods_updated_at
    BEFORE UPDATE ON public.payment_methods
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_payment_transactions_updated_at
    BEFORE UPDATE ON public.payment_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_licenses_updated_at
    BEFORE UPDATE ON public.licenses
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_devices_updated_at
    BEFORE UPDATE ON public.devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE TRIGGER update_license_payments_updated_at
    BEFORE UPDATE ON public.license_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for device limit check
CREATE OR REPLACE TRIGGER check_device_limit_trigger
    BEFORE INSERT ON public.devices
    FOR EACH ROW
    EXECUTE FUNCTION check_device_limit();


-- Create trigger for license validity check
CREATE TRIGGER check_license_validity_trigger
    AFTER INSERT OR UPDATE ON public.licenses
    FOR EACH ROW
    EXECUTE FUNCTION check_license_validity();

-- Add RLS (Row Level Security) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.licenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.license_payments ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view their own data"
    ON public.users FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own data"
    ON public.users FOR UPDATE
    USING (auth.uid() = id);

-- Payment methods policies (admin only)
CREATE POLICY "Only admins can manage payment methods"
    ON public.payment_methods
    USING (auth.role() = 'admin');

-- Payment transactions policies
CREATE POLICY "Users can view their own payment transactions"
    ON public.payment_transactions FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.license_payments
        JOIN public.licenses ON licenses.id = license_payments.license_id
        WHERE license_payments.payment_transaction_id = payment_transactions.id
        AND licenses.user_id = auth.uid()
    ));

-- Licenses policies
CREATE POLICY "Users can view their own licenses"
    ON public.licenses FOR SELECT
    USING (auth.uid() = user_id);

-- Devices policies
CREATE POLICY "Users can view their own devices"
    ON public.devices FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.licenses
        WHERE licenses.id = devices.license_id
        AND licenses.user_id = auth.uid()
    ));

CREATE POLICY "Users can manage their own devices"
    ON public.devices FOR ALL
    USING (EXISTS (
        SELECT 1 FROM public.licenses
        WHERE licenses.id = devices.license_id
        AND licenses.user_id = auth.uid()
    ));

-- License payments policies
CREATE POLICY "Users can view their own license payments"
    ON public.license_payments FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.licenses
        WHERE licenses.id = license_payments.license_id
        AND licenses.user_id = auth.uid()
    ));

-- Insert default payment methods
INSERT INTO public.payment_methods (name, code, config) VALUES
    ('Alipay', 'alipay', '{"api_version": "1.0", "environment": "production"}'),
    ('WeChat Pay', 'wechat', '{"api_version": "v3", "environment": "production"}'),
    ('PayPal', 'paypal', '{"api_version": "v2", "environment": "production"}'),
    ('Stripe', 'stripe', '{"api_version": "2023-10-16", "environment": "production"}')
ON CONFLICT (code) DO NOTHING;

$$ LANGUAGE plpgsql SECURITY DEFINER;