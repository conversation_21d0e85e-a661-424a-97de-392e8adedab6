export interface LicenseAttributes {
    name: string;
    key: string;
    status: string;
    fingerprint: string;
    created: string;
    expiry: string;
    devices_count: number;
}
  
export interface TexSlideLicense {
    data: {
        user: string;
        type: string;
        attributes: LicenseAttributes;
    };
}

export interface TexSlideDevice {
    device_id: string;
    device_name: string;
    created_at: string;
}
  


