"use client";

import { Button } from "@/components/ui/button";
import Link from "next/link";

interface ErrorDisplayProps {
  code?: string | number;
  title?: string;
  message?: string;
  showHomeButton?: boolean;
  showTryAgainButton?: boolean;
  onTryAgain?: () => void;
  onHomeButtonClick?: () => void;
}

export default function ErrorDisplay({
  code = "404",
  title = "Oops!",
  message = "The page you are looking for does not seem to exist.",
  showHomeButton = true,
  showTryAgainButton = false,
  onTryAgain,
  onHomeButtonClick,
}: ErrorDisplayProps) {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-md w-full space-y-8 text-center">
        <h1 className="text-6xl font-bold">{title}</h1>
        <p className="mt-2 text-xl text-gray-600">
          {message}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {showTryAgainButton && (
            <Button
              onClick={onTryAgain}
              className="py-3 px-4 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-md transition-colors"
            >
              Try again
            </Button>
          )}

          {showHomeButton && (
            onHomeButtonClick ? (
              <Button
                onClick={onHomeButtonClick}
                className={`py-3 px-4 ${!showTryAgainButton ? 'bg-blue-500 hover:bg-blue-600 text-white' : 'border border-blue-500 text-blue-500 hover:bg-blue-50'} font-medium rounded-md transition-colors`}
                variant={showTryAgainButton ? "outline" : "default"}
              >
                Back to homepage
              </Button>
            ) : (
              <Button
                asChild
                className={`py-3 px-4 ${!showTryAgainButton ? 'bg-blue-500 hover:bg-blue-600 text-white' : 'border border-blue-500 text-blue-500 hover:bg-blue-50'} font-medium rounded-md transition-colors`}
                variant={showTryAgainButton ? "outline" : "default"}
              >
                <Link href="/">Back to homepage</Link>
              </Button>
            )
          )}
        </div>
      </div>
    </div>
  );
}
