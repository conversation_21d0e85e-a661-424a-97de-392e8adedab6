import { TexSlideLicense } from "@/components/texslide-license";
import { createClient } from "@/utils/supabase/client";

// Custom assert function to replace the one from 'console'
function assert(condition: any, message: string): asserts condition {
  if (!condition) {
    throw new Error(message);
  }
}

// This function can be used on both client and server
export function generateLicenseKey(): string {
  const mapStr: string = '0123456789ABCDEF';

  const rand6str = (): string => {
    return Array.from({length: 6}, () =>
      mapStr[Math.floor(Math.random() * 16)]
    ).join('');
  };

  return Array.from({length: 5}, () => rand6str()).join('-');
};

export async function fetchLicense(userId: string, licenseId: string): Promise<TexSlideLicense> {

  assert(licenseId, 'licenseId is required');

  const supabase = createClient();

  // 初始化 license 对象
  let license: TexSlideLicense = {
    data: {
      user: '',
      type: 'licenses',
      attributes: {
        name: 'Professional Edition',
        key: '',
        status: 'ACTIVE',
        fingerprint: '',
        created: '',
        expiry: '',
        devices_count: 0
      }
    }
  };

  {
    // 获取用户信息
    const { data: userData, error: userError } = await supabase
    .from('users')
    .select('email')
    .eq('id', userId)
    .single();

    if (userError) {
      throw new Error(`Error fetching user: ${userError.message}`);
    }
    license.data.user = userData.email;

    // 获取并更新现有 License
    const { data: existingLicense, error: fetchError } = await supabase
      .from('licenses')
      .select('*')
      .eq('id', licenseId)
      .single();

    if (fetchError) {
      throw new Error(`Error fetching license: ${fetchError.message}`);
    }

    if (!existingLicense) {
      throw new Error('License not found');
    }

    // 获取设备数量
    const { count, error: countError } = await supabase
      .from('devices')
      .select('*', { count: 'exact', head: true })
      .eq('license_id', licenseId);

    if (countError) {
      throw new Error(`Error counting devices: ${countError.message}`);
    }

    // 更新 license 对象
    license.data.attributes.key = existingLicense.license_key;
    license.data.attributes.created = existingLicense.start_date;
    license.data.attributes.expiry = existingLicense.end_date;
    license.data.attributes.devices_count = count || 0;
  }

  return license;
};

export async function upgradeUserLicense(userId: string, licenseId: string) {
  const supabase = createClient();

  try {
    const now = new Date();
    let expiryDate: Date;

    // 获取用户信息
    const { error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError) {
      throw new Error(`Error fetching user: ${userError.message}`);
    }

    // 初始化 license 对象
    let license: TexSlideLicense = {
      data: {
        user: '',
        type: 'licenses',
        attributes: {
          name: 'Professional Edition',
          key: '',
          status: 'ACTIVE',
          fingerprint: '',
          created: '',
          expiry: '',
          devices_count: 0
        }
      }
    };

    if (!licenseId) {
      // 创建新的 License
      const licenseKey = generateLicenseKey();
      expiryDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // 一年后

      // 更新 license 对象
      license.data.attributes.key = licenseKey;
      license.data.attributes.expiry = expiryDate.toISOString();

      const { error: insertError } = await supabase
        .from('licenses')
        .insert({
          user_id: userId,
          license_key: licenseKey,
          status: 'ACTIVE',
          max_devices: 2,
          start_date: now.toISOString(),
          end_date: expiryDate.toISOString(),
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        })
        .select()
        // .eq('user_id', userId)
        .single();

      if (insertError) {
        throw new Error(`Error creating license: ${insertError.message}`);
      }

    } else {
      // 获取并更新现有 License
      const { data: existingLicense, error: fetchError } = await supabase
        .from('licenses')
        .select('*')
        .eq('id', licenseId)
        .single();

      if (fetchError) {
        throw new Error(`Error fetching license: ${fetchError.message}`);
      }

      if (!existingLicense) {
        throw new Error('License not found');
      }

      // 计算新的过期时间
      const currentExpiry = new Date(existingLicense.end_date);
      expiryDate = new Date(Math.max(currentExpiry.getTime(), now.getTime()) + 365 * 24 * 60 * 60 * 1000);

      // 更新 license 对象
      license.data.attributes.key = existingLicense.license_key;
      license.data.attributes.expiry = expiryDate.toISOString();
      license.data.attributes.created = existingLicense.created_at;

      const { error: updateError } = await supabase
        .from('licenses')
        .update({
          status: 'ACTIVE',
          updated_at: now.toISOString(),
          end_date: expiryDate.toISOString()
        })
        .eq('id', licenseId);

      if (updateError) {
        throw new Error(`Error updating license: ${updateError.message}`);
      }
    }

  } catch (error) {
    console.error('Error in upgradeUserLicense:', error);
  }
}